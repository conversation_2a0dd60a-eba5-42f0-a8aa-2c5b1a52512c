import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import type { Database } from '@/types/supabase';

// SLA Configuration Schema
const SLAConfigurationSchema = z.object({
  urgent_deadline_hours: z.number().int().min(1).max(168),
  high_deadline_hours: z.number().int().min(1).max(168),
  medium_deadline_hours: z.number().int().min(1).max(168),
  low_deadline_hours: z.number().int().min(1).max(168),
  is_active: z.boolean().default(true),
  escalation_enabled: z.boolean().default(true),
});

// SLA Configuration Update Schema
const SLAConfigurationUpdateSchema = z
  .object({
    urgent_deadline_hours: z.number().int().min(1).max(168).optional(),
    high_deadline_hours: z.number().int().min(1).max(168).optional(),
    medium_deadline_hours: z.number().int().min(1).max(168).optional(),
    low_deadline_hours: z.number().int().min(1).max(168).optional(),
    is_active: z.boolean().optional(),
    escalation_enabled: z.boolean().optional(),
  })
  .refine(
    (data) => {
      // Ensure deadline progression if values are provided
      const urgent = data.urgent_deadline_hours;
      const high = data.high_deadline_hours;
      const medium = data.medium_deadline_hours;
      const low = data.low_deadline_hours;

      if (urgent && high && urgent >= high) return false;
      if (high && medium && high >= medium) return false;
      if (medium && low && medium >= low) return false;
      if (urgent && medium && urgent >= medium) return false;
      if (urgent && low && urgent >= low) return false;
      if (high && low && high >= low) return false;

      return true;
    },
    {
      message:
        'Deadline hours must follow progression: urgent < high < medium < low',
    }
  );

// Helper function to check admin permissions
async function checkAdminPermissions(userId: string) {
  const serviceClient = createServiceSupabaseClient();

  const { data: currentUser, error: userError } = await serviceClient
    .from('users')
    .select('id, tenant_id, role')
    .eq('clerk_id', userId)
    .single();

  if (userError || !currentUser) {
    throw new Error('User not found in database');
  }

  if (currentUser.role !== 'admin' && currentUser.role !== 'super_admin') {
    throw new Error('Insufficient permissions');
  }

  return currentUser;
}

// Helper function to log SLA configuration changes
async function logSLAConfigurationChange(
  tenantId: string,
  eventType: string,
  configId: string,
  userId: string,
  details: Database['public']['Tables']['sla_escalation_logs']['Insert']['event_details'] = null
) {
  const serviceClient = createServiceSupabaseClient();

  try {
    // Log SLA configuration change
    const { error } = await serviceClient.from('sla_escalation_logs').insert({
      tenant_id: tenantId,
      event_type: eventType,
      sla_configuration_id: configId,
      event_details: details,
      triggered_by: userId,
    });

    if (error) {
      console.error('Failed to log SLA configuration change:', error);
    }
  } catch (error) {
    console.error('Error logging SLA configuration change:', error);
  }
}

// GET - Fetch SLA configuration for the tenant
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Get SLA configuration for the tenant
    const { data: slaConfig, error: configError } = await serviceClient
      .from('sla_configurations')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (configError && configError.code !== 'PGRST116') {
      console.error('SLA configuration fetch error:', configError);
      return NextResponse.json(
        { error: 'Failed to fetch SLA configuration' },
        { status: 500 }
      );
    }

    // If no configuration exists, return default values
    if (!slaConfig) {
      return NextResponse.json({
        success: true,
        data: {
          urgent_deadline_hours: 4,
          high_deadline_hours: 24,
          medium_deadline_hours: 72,
          low_deadline_hours: 168,
          is_active: true,
          escalation_enabled: true,
          exists: false,
        },
      });
    }

    if (slaConfig) {
      return NextResponse.json({
        success: true,
        data: {
          ...slaConfig,
          exists: true,
        },
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          exists: false,
        },
      });
    }
  } catch (error) {
    console.error('Unexpected error in GET /api/settings/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update SLA configuration (admin/super_admin only)
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validation = SLAConfigurationUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid SLA configuration data',
          details: validation.error.issues,
        },
        { status: 400 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Check if configuration exists
    const { data: existingConfig } = await serviceClient
      .from('sla_configurations')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    let updatedConfig;
    let eventType;
    let eventDetails;

    if (existingConfig) {
      // Update existing configuration
      const updateData: Partial<
        Database['public']['Tables']['sla_configurations']['Update']
      > = {
        updated_by: currentUser.id,
        updated_at: new Date().toISOString(),
      };

      // Only include fields that are provided
      if (validation.data.urgent_deadline_hours !== undefined) {
        updateData.urgent_deadline_hours =
          validation.data.urgent_deadline_hours;
      }
      if (validation.data.high_deadline_hours !== undefined) {
        updateData.high_deadline_hours = validation.data.high_deadline_hours;
      }
      if (validation.data.medium_deadline_hours !== undefined) {
        updateData.medium_deadline_hours =
          validation.data.medium_deadline_hours;
      }
      if (validation.data.low_deadline_hours !== undefined) {
        updateData.low_deadline_hours = validation.data.low_deadline_hours;
      }
      if (validation.data.is_active !== undefined) {
        updateData.is_active = validation.data.is_active;
      }
      if (validation.data.escalation_enabled !== undefined) {
        updateData.escalation_enabled = validation.data.escalation_enabled;
      }

      const { data, error } = await serviceClient
        .from('sla_configurations')
        .update(updateData)
        .eq('tenant_id', currentUser.tenant_id)
        .select()
        .single();

      if (error) {
        console.error('SLA configuration update error:', error);
        return NextResponse.json(
          { error: 'Failed to update SLA configuration' },
          { status: 500 }
        );
      }

      updatedConfig = data;
      eventType = 'sla_configuration_updated';
      eventDetails = {
        previous_config: existingConfig,
        updated_fields: Object.keys(validation.data),
      };
    } else {
      // Create new configuration
      const { data, error } = await serviceClient
        .from('sla_configurations')
        .insert({
          tenant_id: currentUser.tenant_id,
          urgent_deadline_hours: validation.data.urgent_deadline_hours || 4,
          high_deadline_hours: validation.data.high_deadline_hours || 24,
          medium_deadline_hours: validation.data.medium_deadline_hours || 72,
          low_deadline_hours: validation.data.low_deadline_hours || 168,
          is_active: validation.data.is_active ?? true,
          escalation_enabled: validation.data.escalation_enabled ?? true,
          created_by: currentUser.id,
          updated_by: currentUser.id,
        })
        .select()
        .single();

      if (error) {
        console.error('SLA configuration creation error:', error);
        return NextResponse.json(
          { error: 'Failed to create SLA configuration' },
          { status: 500 }
        );
      }

      updatedConfig = data;
      eventType = 'sla_configuration_created';
      eventDetails = {
        initial_config: validation.data,
      };
    }

    // Log the configuration change
    await logSLAConfigurationChange(
      currentUser.tenant_id,
      eventType,
      updatedConfig?.id || 'unknown',
      currentUser.id,
      eventDetails
    );

    return NextResponse.json({
      success: true,
      data: updatedConfig,
      message: existingConfig
        ? 'SLA configuration updated successfully'
        : 'SLA configuration created successfully',
    });
  } catch (error) {
    console.error('Unexpected error in PUT /api/settings/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create SLA configuration (admin/super_admin only)
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validation = SLAConfigurationSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid SLA configuration data',
          details: validation.error.issues,
        },
        { status: 400 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Check if configuration already exists
    const { data: existingConfig } = await serviceClient
      .from('sla_configurations')
      .select('id')
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (existingConfig) {
      return NextResponse.json(
        {
          error:
            'SLA configuration already exists for this tenant. Use PUT to update.',
        },
        { status: 409 }
      );
    }

    // Create new configuration
    const { data: newConfig, error } = await serviceClient
      .from('sla_configurations')
      .insert({
        tenant_id: currentUser.tenant_id,
        urgent_deadline_hours: validation.data.urgent_deadline_hours || 4,
        high_deadline_hours: validation.data.high_deadline_hours || 24,
        medium_deadline_hours: validation.data.medium_deadline_hours || 72,
        low_deadline_hours: validation.data.low_deadline_hours || 168,
        is_active: validation.data.is_active ?? true,
        escalation_enabled: validation.data.escalation_enabled ?? true,
        created_by: currentUser.id,
        updated_by: currentUser.id,
      })
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to create SLA configuration' },
        { status: 500 }
      );
    }

    // Log the configuration creation
    await logSLAConfigurationChange(
      currentUser.tenant_id,
      'sla_configuration_created',
      newConfig?.id || 'unknown',
      currentUser.id,
      { initial_config: validation.data }
    );

    return NextResponse.json({
      success: true,
      data: newConfig,
      message: 'SLA configuration created successfully',
    });
  } catch (error) {
    console.error('Unexpected error in POST /api/settings/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Reset SLA configuration to defaults (admin/super_admin only)
export async function DELETE() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Get existing configuration for logging
    const { data: existingConfig } = await serviceClient
      .from('sla_configurations')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (!existingConfig) {
      return NextResponse.json(
        { error: 'No SLA configuration found to delete' },
        { status: 404 }
      );
    }

    // Reset to default values instead of deleting
    const defaultConfig = {
      urgent_deadline_hours: 4,
      high_deadline_hours: 24,
      medium_deadline_hours: 72,
      low_deadline_hours: 168,
      is_active: true,
      escalation_enabled: true,
      updated_by: currentUser.id,
      updated_at: new Date().toISOString(),
    };

    const { data: resetConfig, error } = await serviceClient
      .from('sla_configurations')
      .update(defaultConfig)
      .eq('tenant_id', currentUser.tenant_id)
      .select()
      .single();

    if (error) {
      console.error('SLA configuration reset error:', error);
      return NextResponse.json(
        { error: 'Failed to reset SLA configuration' },
        { status: 500 }
      );
    }

    // Log the configuration reset
    await logSLAConfigurationChange(
      currentUser.tenant_id,
      'sla_configuration_updated',
      resetConfig?.id || 'unknown',
      currentUser.id,
      {
        action: 'reset_to_defaults',
        previous_config: existingConfig,
        reset_to: defaultConfig,
      }
    );

    return NextResponse.json({
      success: true,
      data: resetConfig,
      message: 'SLA configuration reset to defaults successfully',
    });
  } catch (error) {
    console.error('Unexpected error in DELETE /api/settings/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
