import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { Button } from '@/features/shared/components/ui/button';

interface ReplyDraftConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDiscard: () => void;
  onSave: () => void;
}

/**
 * Confirmation dialog for reply draft navigation
 * Shows when user tries to navigate away with unsaved reply content
 */
export function ReplyDraftConfirmDialog({
  open,
  onOpenChange,
  onDiscard,
  onSave,
}: ReplyDraftConfirmDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Unsaved Reply</DialogTitle>
          <DialogDescription>
            You have unsaved changes in your reply. What would you like to do?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className='gap-2'>
          <Button variant='outline' onClick={onDiscard}>
            Discard Changes
          </Button>
          <Button onClick={onSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
