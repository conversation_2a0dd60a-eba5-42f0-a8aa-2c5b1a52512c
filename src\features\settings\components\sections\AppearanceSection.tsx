'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { ThemeSelector } from '../ThemeSelector';

export function AppearanceSection() {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>Appearance</h2>
        <p className='text-sm text-muted-foreground'>
          Customize the appearance of the application.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Theme</CardTitle>
          <CardDescription>
            Choose your preferred theme for the application.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ThemeSelector />
        </CardContent>
      </Card>
    </div>
  );
}
