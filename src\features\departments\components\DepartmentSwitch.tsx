'use client';

import * as React from 'react';
import * as SwitchPrimitive from '@radix-ui/react-switch';
import { cn } from '@/lib/utils';

interface DepartmentSwitchProps
  extends React.ComponentProps<typeof SwitchPrimitive.Root> {
  departmentColor?: string; // Expected to be a dot color like 'bg-blue-500'
}

function DepartmentSwitch({
  className,
  departmentColor,
  checked,
  ...props
}: DepartmentSwitchProps) {
  // Use the department color directly (expected to be a dot color like 'bg-blue-500')
  const activeBackgroundColor = departmentColor || 'bg-primary';
  const isChecked = Boolean(checked);

  return (
    <SwitchPrimitive.Root
      data-slot='switch'
      className={cn(
        'peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50',
        isChecked
          ? `${activeBackgroundColor} data-[state=checked]:${activeBackgroundColor}`
          : 'bg-gray-200 data-[state=unchecked]:bg-gray-200',
        className
      )}
      checked={isChecked}
      {...props}
    >
      <SwitchPrimitive.Thumb
        className={cn(
          'pointer-events-none block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0'
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { DepartmentSwitch };
