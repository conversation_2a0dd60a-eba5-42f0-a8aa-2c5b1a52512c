'use client';

import { useSyncExternalStore } from 'react';

interface NoSSRProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * NoSSR component to prevent hydration mismatches - React 19 Optimized
 * Uses useSyncExternalStore instead of useEffect for better performance
 * Useful for components that depend on browser-only APIs or have dynamic content
 */
export function NoSSR({ children, fallback = null }: NoSSRProps) {
  // React 19 pattern: Use useSyncExternalStore instead of useEffect + useState
  const hasMounted = useSyncExternalStore(
    () => () => {}, // subscribe (no-op since we don't need to listen to changes)
    () => true, // getSnapshot (client-side always returns true)
    () => false // getServerSnapshot (server-side always returns false)
  );

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
