'use server';

import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { CreateTicketFormSchema } from '../models/ticket-form.schema';

export async function createTicket(
  formData: z.infer<typeof CreateTicketFormSchema>
) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error('You must be logged in to create a ticket.');
  }

  const serviceSupabase = createServiceSupabaseClient();

  const { data: userData } = await serviceSupabase
    .from('users')
    .select('id, tenant_id')
    .eq('clerk_id', userId)
    .single();

  if (!userData) {
    throw new Error('User not found.');
  }

  // Extract cc field and other fields that don't belong in the database columns
  const { cc, ...ticketFields } = formData;

  const newTicket = {
    ...ticketFields,
    tenant_id: userData.tenant_id,
    created_by: userData.id,
    status: 'new',
    // Store cc in metadata if provided
    metadata: cc && cc.length > 0 ? { cc } : {},
  };

  const { data, error } = await serviceSupabase
    .from('tickets')
    .insert(newTicket)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}
