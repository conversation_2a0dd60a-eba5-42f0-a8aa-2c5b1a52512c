'use client';

import { useState } from 'react';
import { ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CollapsedMessagesIndicatorProps {
  count: number;
  onExpand: () => void;
  isLoading?: boolean;
  className?: string;
}

export function CollapsedMessagesIndicator({
  count,
  onExpand,
  isLoading = false,
  className,
}: CollapsedMessagesIndicatorProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className={cn('flex py-4', className)}>
      {/* Double border divider line */}
      <div className='w-full border-t border-gray-200 dark:border-gray-700 relative'>
        <div className='absolute inset-x-0 top-[2px] border-t border-gray-200 dark:border-gray-700' />

        {/* Circular indicator positioned on the divider, aligned with avatars */}
        <button
          onClick={onExpand}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          disabled={isLoading}
          className={cn(
            'absolute left-6 top-1/2 transform -translate-y-1/2',
            'flex items-center justify-center w-10 h-10 rounded-full',
            'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
            'hover:bg-gray-50 dark:hover:bg-gray-700',
            'focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'transition-colors duration-200 cursor-pointer'
          )}
          title={`Show ${count} hidden message${count !== 1 ? 's' : ''}`}
        >
          {isLoading ? (
            <div className='w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin' />
          ) : isHovered ? (
            <ChevronsUpDown className='w-4 h-4 text-gray-600 dark:text-gray-400' />
          ) : (
            <span className='text-sm font-medium text-gray-600 dark:text-gray-400'>
              {count}
            </span>
          )}
        </button>
      </div>
    </div>
  );
}
