'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser, useAuth } from '@clerk/nextjs';
import { useRef, useEffect } from 'react';

// Global sync state to prevent multiple sync attempts across components
const globalSyncState = new Map<string, boolean>();

export interface SyncStatus {
  isLoading: boolean;
  isComplete: boolean;
  error: string | null;
  needsSync: boolean;
  tenantExists: boolean;
  userExists: boolean;
}

export interface SyncStatusResponse {
  needsSync: boolean;
  tenantExists: boolean;
  userExists: boolean;
}

export interface SyncData {
  tenant?: { id: string; name: string; [key: string]: unknown };
  user?: { id: string; email: string; [key: string]: unknown };
  organization?: {
    id: string;
    name: string;
    slug: string;
    [key: string]: unknown;
  };
}

/**
 * Modern 2025 token readiness detection - minimal & effective
 * Polls for token availability with exponential backoff
 */
async function waitForToken(
  getToken: (options?: { template?: string }) => Promise<string | null>,
  maxAttempts = 6
): Promise<string | null> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      // Try with supabase template first, fallback to default
      let token = await getToken({ template: 'supabase' });
      if (!token) {
        token = await getToken();
      }

      if (token) {
        return token;
      }
    } catch {
      // Silent retry - no console spam
    }

    // Exponential backoff: 100ms, 200ms, 400ms, 800ms, 1600ms...
    const delay = Math.min(100 * Math.pow(2, attempt), 1000);
    await new Promise((resolve) => setTimeout(resolve, delay));
  }

  return null;
}

/**
 * Modern 2025 React Query-based sync hook
 * Eliminates race conditions and 401 errors through proper dependency management
 */
export function useClerkSupabaseSync(tenantId: string | null) {
  const { user, isLoaded, isSignedIn } = useUser();
  const { getToken } = useAuth();
  const queryClient = useQueryClient();
  const syncCompleteRef = useRef(false);
  const lastUserIdRef = useRef<string | null>(null);

  // Reset sync state when user changes
  if (user?.id !== lastUserIdRef.current) {
    syncCompleteRef.current = false;
    lastUserIdRef.current = user?.id || null;
  }

  // Enhanced authentication readiness check with token validation
  const tokenReadinessQuery = useQuery({
    queryKey: ['token-readiness', user?.id],
    queryFn: async () => {
      const token = await waitForToken(getToken);
      return !!token;
    },
    enabled: isLoaded && isSignedIn && !!user,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
    refetchOnWindowFocus: false,
  });

  // Only proceed when both auth and token are ready
  const isFullyReady = tokenReadinessQuery.data && !!tenantId;

  // React Query v5 pattern: Check sync status with enhanced enabled condition
  const syncStatusQuery = useQuery<SyncStatusResponse>({
    queryKey: ['sync-status', tenantId, user?.id],
    queryFn: async (): Promise<SyncStatusResponse> => {
      const token = await waitForToken(getToken);
      if (!token) {
        console.warn('🔄 Sync check: No token available');
        return { needsSync: true, tenantExists: false, userExists: false };
      }

      const response = await fetch(
        `/api/sync?tenant_id=${encodeURIComponent(tenantId!)}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        console.warn(
          `🔄 Sync check failed: ${response.status} ${response.statusText}`
        );
        // If sync check fails, assume sync is needed
        return { needsSync: true, tenantExists: false, userExists: false };
      }

      const data = await response.json();
      const syncStatus = data.syncStatus || {
        needsSync: true,
        tenantExists: false,
        userExists: false,
      };

      console.log('🔄 Sync status check result:', syncStatus);
      return syncStatus;
    },
    enabled: !!(isFullyReady && !syncCompleteRef.current),
    staleTime: 30 * 60 * 1000, // 30 minutes - sync status rarely changes once complete
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: false, // Don't retry sync checks
    refetchOnWindowFocus: false, // Don't refetch on focus
    refetchOnMount: false, // Don't refetch on mount after first success
    refetchInterval: false, // Disable automatic refetching
    refetchOnReconnect: false, // Don't refetch on network reconnect
  });

  // React Query v5 pattern: Perform sync with mutation
  const syncMutation = useMutation({
    mutationKey: ['perform-sync', tenantId, user?.id],
    mutationFn: async () => {
      const token = await waitForToken(getToken);
      if (!token) {
        throw new Error('Authentication token not available');
      }

      const response = await fetch('/api/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ tenantId }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Sync failed');
      }

      return result.data || {};
    },
    onSuccess: () => {
      // Invalidate sync status to refetch
      queryClient.invalidateQueries({
        queryKey: ['sync-status', tenantId, user?.id],
      });
    },
    retry: false, // Don't retry sync operations
  });

  // Simple auto-trigger sync once per session
  const syncStatus = syncStatusQuery.data;

  // Use useEffect with global sync state to prevent multiple triggers across all components
  useEffect(() => {
    const syncKey = `${tenantId}-${user?.id}`;

    if (
      syncStatus?.needsSync &&
      !syncMutation.isPending &&
      !syncMutation.isSuccess &&
      !syncCompleteRef.current &&
      !globalSyncState.get(syncKey) &&
      isFullyReady &&
      tenantId &&
      user?.id
    ) {
      console.log('🔄 Auto-triggering sync for tenant:', tenantId);
      syncCompleteRef.current = true; // Prevent multiple triggers in this component
      globalSyncState.set(syncKey, true); // Prevent multiple triggers globally
      syncMutation.mutate();
    }
  }, [syncStatus?.needsSync, isFullyReady, tenantId, user?.id, syncMutation]); // Include syncMutation

  // Return modern React Query-based interface
  const isLoading = syncStatusQuery.isLoading || syncMutation.isPending;
  const isComplete =
    !isLoading &&
    !syncStatus?.needsSync &&
    (syncStatus?.needsSync === false || syncMutation.isSuccess);
  const error = syncStatusQuery.error || syncMutation.error;

  return {
    isLoading,
    isComplete,
    error: error?.message || null,
    needsSync: syncStatus?.needsSync ?? true,
    tenantExists: syncStatus?.tenantExists ?? false,
    userExists: syncStatus?.userExists ?? false,
  };
}
