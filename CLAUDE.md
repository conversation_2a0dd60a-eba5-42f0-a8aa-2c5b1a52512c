# CLAUDE Guidelines

**Development Rules and AI Collaboration Guide**

---

## Core Philosophy

1. **Keep It Simple**
   Write clear, maintainable, and focused code. Avoid complexity unless it is necessary for correctness or scalability.

2. **Improve What Works**
   Prioritize enhancing existing, functional logic rather than rebuilding from scratch. Rewrites should only happen when essential or explicitly required.

3. **Stay on Task**
   Remain aligned with the immediate objective. Do not introduce unrelated improvements or expand scope without review.

4. **Maintain Quality**
   All code must be clean, organized, tested, secure, and consistent with established standards.

5. **Work Transparently**
   Document assumptions, explain decisions, and maintain alignment with both human and AI collaborators.

---

## Prohibited Practices and Modern Standards

1. **No Forced Redirects or Routing Bypasses**
   Avoid client-side history manipulation or forced navigations that bypass user intention or app logic. Always follow the application’s routing and authentication mechanisms.

2. **No Mock Data in Production**
   Production environments must never include mock data. Mocks may only be used in isolated test contexts or explicitly scaffolded development sandboxes.

3. **Follow Modern Best Practices**
   All implementation must align with 2025+ best practices.
   - Keep all libraries and frameworks up-to-date.
   - Prefer officially supported and widely adopted tools.
   - Reference architecture and tech docs to validate future-proof choices.

---

## Task Execution and Workflow

1. **Define the Task Clearly**
   Understand the goal, scope, expected behavior, and constraints. Confirm any dependencies or edge cases.

2. **Change Management**
   - Identify all affected areas and dependencies.
   - Plan changes logically—implement them one step at a time.
   - Define test strategy before writing implementation code.

3. **Track Progress Transparently**
   Use inline comments, atomic commits, and update a shared status log if available (e.g., `status.md`).

---

## AI Collaboration and Prompting

1. **Communicate Clearly**
   When working with AI, define the task goal, constraints, and any relevant context or files.

2. **Be Precise with Context**
   Reference specific flows, file paths, code blocks, or documents to guide AI correctly.

3. **Differentiate Suggestion vs. Application**
   - Use `Suggestion:` when asking for feedback or an alternate approach.
   - Use `Applying fix:` for reviewed, verified changes ready to be integrated.

4. **Review AI Output Critically**
   AI suggestions must be reviewed for logic, architectural consistency, and alignment with standards.

5. **Keep Tasks Atomic**
   Break large tasks into focused prompts. This improves response quality and control.

6. **Use AI Where It Excels**
   Use AI for boilerplate, refactors, testing, and validation logic. Always manually review logic-heavy or security-sensitive changes.

7. **AI Prompting Example**

   ```
   Confirming understanding: Reviewed docs/FutureStructureGuide.md. Goal is to refactor auth flow to match the planned folder structure. Proceeding with isolated login module first.
   ```

---

## Code Quality and Style

1. **Apply Design Principles**
   Follow YAGNI, KISS, DRY, and SOLID principles.

2. **Use Strict Typing (TypeScript)**
   Type safety is mandatory. Avoid `any`. Use `unknown`, generics, or interfaces when needed. Document using JSDoc.

3. **Write Readable, Modular Code**
   Favor small, focused modules. Large files should be broken down if over 300 lines.

4. **Eliminate Redundancy**
   Reuse utilities and logic. Do not duplicate functionality.

5. **Avoid Premature Optimization**
   Optimize only when necessary. Focus first on clarity and maintainability.

6. **Follow Formatting Rules**
   Comply with ESLint and Prettier configurations.

7. **Use Descriptive Naming**
   Variable, function, and file names must be clear, consistent, and pattern-aligned. Avoid temporary names like `temp`, `test`, or `copy`.

8. **No Throwaway Code**
   Never commit one-time scripts or experimental files into the main codebase.

9. **Use ShadCN UI**
   All UI components must use the ShadCN library unless an exception is approved. Follow its structure, accessibility, and styling conventions.

10. **Support Dark Mode**
    All UI work must respect dark and light theme toggling, using the project's theme utilities.

---

## Refactoring Rules

1. **Have a Purpose**
   Refactor only to improve clarity, remove duplication, or align with structural goals.

2. **Think Holistically**
   When refactoring, check adjacent files and modules for related improvements.

3. **Avoid Duplicate Versions**
   Never create alternate files like `v2`, `copy`, or `backup`. Modify the original or migrate it properly.

4. **Test All Integration Points**
   After a refactor, verify that all upstream and downstream logic still works as expected.

---

## Testing and Validation

1. **Use Test-Driven Development Where Possible**
   Write tests for edge cases before implementing features.
   Reproduce bugs with failing tests, then apply a fix.

2. **Ensure Full Coverage**
   Cover all critical logic with unit, integration, and E2E tests.

3. **Only Commit on Passing Tests**
   Never commit broken or unstable test suites.

4. **Use Realistic Mock Data**
   Mocks should resemble real data and only be used in test environments.

5. **Manually Verify Changes**
   All visual and interaction changes must be tested manually as well as programmatically.

---

## Debugging and Troubleshooting

1. **Fix the Root Cause**
   Do not apply patches that ignore the underlying issue.

2. **Use Logs Intentionally**
   Log only what is useful. Clean up verbose or unnecessary logs after debugging.

3. **Maintain Fix Logs (Optional)**
   Use the `fixes/` directory to track recurring problems and document how they were resolved.

4. **Add Strategic Fix Notes**
   When resolving complex issues, document your approach in a clear and permanent location.

5. **Always Research First**
   Before escalating, check the documentation, known issues, and online communities.

---

## Security Standards

1. **Always Validate Server-Side**
   Never trust frontend-only logic for handling sensitive data or permissions.

2. **Sanitize All Input**
   All user input must be validated and sanitized both client- and server-side.

3. **Audit Dependencies**
   Review and evaluate all third-party libraries before adding them to the project.

4. **Protect Secrets**
   Never expose tokens or sensitive keys. Use environment variables and secure access policies.

---

## Version Control and Environment

1. **Follow Git Best Practices**
   Commit often with atomic changes and clear messages. Use `.gitignore` properly to exclude noise.

2. **Branch Responsibly**
   Follow naming conventions. Do not create untracked or unnecessary branches.

3. **Protect Environment Variables**
   Never commit `.env` files. Use `.env.example` for safe configuration sharing.

4. **Respect Environment Guards**
   Ensure dev, staging, and prod configs are clearly separated and enforced.

5. **Restart on Critical Changes**
   Restart backend or frontend servers when core logic or environment configs are updated.

---

## Runtime, Terminal, and Server Handling

1. **Do Not Start Redundant Servers**
   Kill existing processes before running `npm run dev`.

2. **Use MCP Terminal Tools**
   Use `list-processes`, `kill-process`, and proper terminal IDs to manage runtime processes.

3. **Avoid New Ports**
   Reuse existing ports. Do not open new ports unless explicitly instructed.

---

## Codebase Discipline

1. **Inspect Before Changing**
   Always use a codebase retrieval step to gather all related logic before making changes.

2. **Understand Context**
   Review dependencies, imports, and component usage before editing any file.

3. **Check Before Writing**
   Search for similar or existing utilities before creating new ones.

4. **Never Copy Blindly**
   Understand every line of code you use, especially from external sources or AI suggestions.

---

## AI Response and Review Expectations

1. **No Hallucinations**
   If something is unknown, clearly state “I don’t know.”

2. **Be Specific**
   Avoid vague explanations. Address the exact code, flow, or error.

3. **Explain Mistakes**
   When identifying issues, analyze and explain the actual problem with examples.

4. **Check Before Claiming**
   Validate facts by reading the relevant code and context.

5. **Declare Uncertainty**
   Clearly communicate when assumptions are being made. Ask for confirmation.

6. **Challenge Assumptions**
   Don’t rely on guesswork. Always verify project-specific patterns, configs, or user preferences.

---

## State Management Roles

- **Zustand** – Holds local UI state and acts as the single source of truth.
- **React Query** – Handles server data syncing, caching, and fetching.
- **IndexedDB** – Persists data offline for local-first behavior.
- **Supabase Realtime** – Ensures live state updates across tabs and layers.

### Real-Time Sync Benefits

1. Keeps UI fresh without flickering.
2. Reduces network load via longer stale times.
3. Aligns Zustand, IndexedDB, and Query layers.
4. Keeps multiple tabs consistent in real-time.

---

## File Management and Cleanup

1. **Check Before Creating**
   Always look for existing files or utilities before creating new ones.

2. **Clean Up As You Work**
   Delete unused logic, files, or folders during your task—not afterward.

3. **Remove Legacy Code**
   If replacing logic, remove the obsolete implementation in the same commit.

---

## ESLint Enforcement Rules

1. No `any`. Use specific types or `unknown`.
2. No unused variables.
3. Exported functions must have return types.
4. Don’t type things the compiler can infer.
5. Remove all `console.log` unless replaced with proper logging.
6. Add `key` props in JSX lists.
7. Keep hook dependencies accurate.
8. No `@ts-ignore`. Fix the type instead.
9. Comment intentional empty functions.
10. Never use undefined variables.
11. Avoid the non-null `!` operator.
12. Prefer `const` by default.
13. Use `===` not `==`.
14. Avoid variable shadowing.
15. Never be lazy. Find root causes and fix them properly.
16. Make all fixes as small and isolated as possible. Simplicity over cleverness.
