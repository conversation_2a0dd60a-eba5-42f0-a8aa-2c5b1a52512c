# CLAUDE Guidelines

**Development Rules and AI Collaboration Guide**

---

## Core Philosophy

- Keep It Simple
  Write clear, maintainable, and focused code. Avoid complexity unless it is necessary for correctness or scalability.

- Improve What Works
  Prioritize enhancing existing, functional logic rather than rebuilding from scratch. Rewrites should only happen when essential or explicitly required.

- Stay on Task
  Remain aligned with the immediate objective. Do not introduce unrelated improvements or expand scope without review.

- Maintain Quality
  All code must be clean, organized, tested, secure, and consistent with established standards.

- Work Transparently
  Document assumptions, explain decisions, and maintain alignment with both human and AI collaborators.

---

## Prohibited Practices and Modern Standards

- No Forced Redirects or Routing Bypasses
  Avoid client-side history manipulation or forced navigations that bypass user intention or app logic. Always follow the application’s routing and authentication mechanisms.

- No Mock Data in Production
  Production environments must never include mock data. Mocks may only be used in isolated test contexts or explicitly scaffolded development sandboxes.

- Follow Modern Best Practices
  All implementation must align with 2025+ best practices.
  - Keep all libraries and frameworks up-to-date.
  - Prefer officially supported and widely adopted tools.
  - Reference architecture and tech docs to validate future-proof choices.

---

## Task Execution and Workflow

- Define the Task Clearly
  Understand the goal, scope, expected behavior, and constraints. Confirm any dependencies or edge cases.

- Change Management
  - Identify all affected areas and dependencies.
  - Plan changes logically—implement them one step at a time.
  - Define test strategy before writing implementation code.

- Track Progress Transparently
  Use inline comments, atomic commits, and update a shared status log if available (e.g., `status.md`).

---

## AI Collaboration and Prompting

- Communicate Clearly
  When working with AI, define the task goal, constraints, and any relevant context or files.

- Be Precise with Context
  Reference specific flows, file paths, code blocks, or documents to guide AI correctly.

- Differentiate Suggestion vs. Application
  - Use `Suggestion:` when asking for feedback or an alternate approach.
  - Use `Applying fix:` for reviewed, verified changes ready to be integrated.

- Review AI Output Critically
  AI suggestions must be reviewed for logic, architectural consistency, and alignment with standards.

- Keep Tasks Atomic
  Break large tasks into focused prompts. This improves response quality and control.

- Use AI Where It Excels
  Use AI for boilerplate, refactors, testing, and validation logic. Always manually review logic-heavy or security-sensitive changes.

---

## Code Quality and Style

- Apply Design Principles
  Follow YAGNI, KISS, DRY, and SOLID principles.

- Use Strict Typing (TypeScript)
  Type safety is mandatory. Avoid `any`. Use `unknown`, generics, or interfaces when needed. Document using JSDoc.

- Write Readable, Modular Code
  Favor small, focused modules. Large files should be broken down if over 300 lines.

- Eliminate Redundancy
  Reuse utilities and logic. Do not duplicate functionality.

- Avoid Premature Optimization
  Optimize only when necessary. Focus first on clarity and maintainability.

- Follow Formatting Rules
  Comply with ESLint and Prettier configurations.

- Use Descriptive Naming
  Variable, function, and file names must be clear, consistent, and pattern-aligned. Avoid temporary names like `temp`, `test`, or `copy`.

- No Throwaway Code
  Never commit one-time scripts or experimental files into the main codebase.

- Use ShadCN UI
  All UI components must use the ShadCN library unless an exception is approved. Follow its structure, accessibility, and styling conventions.

- Support Dark Mode
  All UI work must respect dark and light theme toggling, using the project's theme utilities.

---

## Refactoring Rules

- Have a Purpose
  Refactor only to improve clarity, remove duplication, or align with structural goals.

- Think Holistically
  When refactoring, check adjacent files and modules for related improvements.

- Avoid Duplicate Versions
  Never create alternate files like `v2`, `copy`, or `backup`. Modify the original or migrate it properly.

- Test All Integration Points
  After a refactor, verify that all upstream and downstream logic still works as expected.

---

## Testing and Validation

- Use Test-Driven Development Where Possible
  Write tests for edge cases before implementing features.
  Reproduce bugs with failing tests, then apply a fix.

- Ensure Full Coverage
  Cover all critical logic with unit, integration, and E2E tests.

- Only Commit on Passing Tests
  Never commit broken or unstable test suites.

- Use Realistic Mock Data
  Mocks should resemble real data and only be used in test environments.

- Manually Verify Changes
  All visual and interaction changes must be tested manually as well as programmatically.

---

## Debugging and Troubleshooting

- Fix the Root Cause
  Do not apply patches that ignore the underlying issue.

- Use Logs Intentionally
  Log only what is useful. Clean up verbose or unnecessary logs after debugging.

- Maintain Fix Logs (Optional)
  Use the `fixes/` directory to track recurring problems and document how they were resolved.

- Add Strategic Fix Notes
  When resolving complex issues, document your approach in a clear and permanent location.

- Always Research First
  Before escalating, check the documentation, known issues, and online communities.

---

## Security Standards

- Always Validate Server-Side
  Never trust frontend-only logic for handling sensitive data or permissions.

- Sanitize All Input
  All user input must be validated and sanitized both client- and server-side.

- Audit Dependencies
  Review and evaluate all third-party libraries before adding them to the project.

- Protect Secrets
  Never expose tokens or sensitive keys. Use environment variables and secure access policies.

---

## Version Control and Environment

- Follow Git Best Practices
  Commit often with atomic changes and clear messages. Use `.gitignore` properly to exclude noise.

- Branch Responsibly
  Follow naming conventions. Do not create untracked or unnecessary branches.

- Protect Environment Variables
  Never commit `.env` files. Use `.env.example` for safe configuration sharing.

- Respect Environment Guards
  Ensure dev, staging, and prod configs are clearly separated and enforced.

- Restart on Critical Changes
  Restart backend or frontend servers when core logic or environment configs are updated.

---

## Runtime, Terminal, and Server Handling

- Do Not Start Redundant Servers
  Kill existing processes before running `npm run dev`.

- Use MCP Terminal Tools
  Use `list-processes`, `kill-process`, and proper terminal IDs to manage runtime processes.

- Avoid New Ports
  Reuse existing ports. Do not open new ports unless explicitly instructed.

---

## Codebase Discipline

- Inspect Before Changing
  Always use a codebase retrieval step to gather all related logic before making changes.

- Understand Context
  Review dependencies, imports, and component usage before editing any file.

- Check Before Writing
  Search for similar or existing utilities before creating new ones.

- Never Copy Blindly
  Understand every line of code you use, especially from external sources or AI suggestions.

---

## AI Response and Review Expectations

- No Hallucinations
  If something is unknown, clearly state “I don’t know.”

- Be Specific
  Avoid vague explanations. Address the exact code, flow, or error.

- Explain Mistakes
  When identifying issues, analyze and explain the actual problem with examples.

- Check Before Claiming
  Validate facts by reading the relevant code and context.

- Declare Uncertainty
  Clearly communicate when assumptions are being made. Ask for confirmation.

- Challenge Assumptions
  Don’t rely on guesswork. Always verify project-specific patterns, configs, or user preferences.

---

## State Management Roles

- Zustand – Holds local UI state and acts as the single source of truth.
- React Query – Handles server data syncing, caching, and fetching.
- IndexedDB – Persists data offline for local-first behavior.
- Supabase Realtime – Ensures live state updates across tabs and layers.

### Real-Time Sync Benefits

- Keeps UI fresh without flickering.
- Reduces network load via longer stale times.
- Aligns Zustand, IndexedDB, and Query layers.
- Keeps multiple tabs consistent in real-time.

---

## File Management and Cleanup

- Check Before Creating
  Always look for existing files or utilities before creating new ones.

- Clean Up As You Work
  Delete unused logic, files, or folders during your task—not afterward.

- Remove Legacy Code
  If replacing logic, remove the obsolete implementation in the same commit.

---

## ESLint Enforcement Rules

- No `any`. Use specific types or `unknown`.
- No unused variables.
- Exported functions must have return types.
- Don’t type things the compiler can infer.
- Remove all `console.log` unless replaced with proper logging.
- Add `key` props in JSX lists.
- Keep hook dependencies accurate.
- No `@ts-ignore`. Fix the type instead.
- Comment intentional empty functions.
- Never use undefined variables.
- Avoid the non-null `!` operator.
- Prefer `const` by default.
- Use `===` not `==`.
- Avoid variable shadowing.

---

## Developer Mindset & Execution Discipline

- Always fix the root cause. Do not apply temporary patches or band-aid solutions. If there’s a bug, investigate it fully, understand what caused it, and resolve it at the source.

- Never be lazy. Take responsibility for your work. You are expected to think and act like a senior developer—disciplined, thorough, and accountable.

- Keep all changes as simple as humanly possible. Every fix or update should impact only the code that is absolutely necessary for the task.

- Do not overextend changes. Avoid side effects, ripple edits, or unnecessary optimizations unless explicitly required.

- Simplicity is not optional. Your goal is to deliver clean, minimal, and bug-free code that is easy to maintain and understand.

- Never make assumptions. If something is unclear, ask. If something feels uncertain, verify it. Precision matters.

- Strive for clarity in everything—code, commits, communication, and collaboration.
