import React from 'react';
import { Button } from '@/features/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import { Separator } from '@/features/shared/components/ui/separator';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Paperclip,
  type LucideIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CustomEditor, CustomText } from './slate-types';

interface ToolbarProps {
  editor: CustomEditor;
  disabled: boolean;
  onAttachClick: (() => void) | undefined;
  toggleMark: (format: keyof Omit<CustomText, 'text'>) => void;
  toggleBlock: (format: string) => void;
}

const ToolbarButton = ({
  icon: Icon,
  isActive,
  onClick,
  disabled,
}: {
  format: string;
  icon: LucideIcon;
  isBlock?: boolean;
  isActive: boolean;
  onClick: () => void;
  disabled: boolean;
}) => (
  <Button
    type='button'
    variant='ghost'
    size='sm'
    className={cn(
      'h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700',
      isActive && 'bg-gray-200 dark:bg-gray-600'
    )}
    onClick={onClick}
    disabled={disabled}
  >
    <Icon className='h-4 w-4' />
  </Button>
);

export function Toolbar({
  disabled,
  onAttachClick,
  toggleMark,
  toggleBlock,
}: ToolbarProps) {
  return (
    <div className='space-y-2 mb-4'>
      <div className='flex items-center gap-1'>
        <Select
          key='format-select'
          defaultValue='paragraph'
          onValueChange={(value) => {
            if (disabled) return;
            if (value === 'heading1') {
              toggleBlock('heading-one');
            } else if (value === 'heading2') {
              toggleBlock('heading-two');
            } else {
              toggleBlock('paragraph');
            }
          }}
        >
          <SelectTrigger className='w-32 h-8'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='paragraph'>
              <span className='text-sm'>Paragraph</span>
            </SelectItem>
            <SelectItem value='heading1'>
              <span className='text-lg font-bold'>Heading 1</span>
            </SelectItem>
            <SelectItem value='heading2'>
              <span className='text-base font-semibold'>Heading 2</span>
            </SelectItem>
          </SelectContent>
        </Select>

        <Separator orientation='vertical' className='h-6 mx-1' />

        <ToolbarButton
          format='bold'
          icon={Bold}
          isActive={false}
          onClick={() => toggleMark('bold')}
          disabled={disabled}
        />
        <ToolbarButton
          format='italic'
          icon={Italic}
          isActive={false}
          onClick={() => toggleMark('italic')}
          disabled={disabled}
        />
        <ToolbarButton
          format='underline'
          icon={Underline}
          isActive={false}
          onClick={() => toggleMark('underline')}
          disabled={disabled}
        />

        <Separator orientation='vertical' className='h-6 mx-1' />

        <ToolbarButton
          format='bulleted-list'
          icon={List}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('bulleted-list')}
          disabled={disabled}
        />
        <ToolbarButton
          format='numbered-list'
          icon={ListOrdered}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('numbered-list')}
          disabled={disabled}
        />

        <Separator orientation='vertical' className='h-6 mx-1' />

        <Button
          type='button'
          variant='ghost'
          size='sm'
          className='h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700'
          onClick={() => {
            onAttachClick?.();
          }}
          disabled={disabled}
          title='Attach File'
        >
          <Paperclip className='h-4 w-4' />
        </Button>
      </div>
    </div>
  );
}
