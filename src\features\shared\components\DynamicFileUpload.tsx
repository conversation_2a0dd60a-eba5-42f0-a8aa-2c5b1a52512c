'use client';

/**
 * Dynamic FileUpload - Performance Optimized with Code Splitting
 *
 * This component provides a dynamically imported FileUpload to reduce
 * initial bundle size and improve page load performance.
 *
 * Key Features:
 * - Dynamic import with loading state
 * - SSR disabled for better performance
 * - Proper TypeScript support
 * - Fallback loading component
 *
 * <AUTHOR> Augster
 * @version 1.0 - Dynamic Import Optimized (January 2025)
 */

import dynamic from 'next/dynamic';
import { ComponentProps } from 'react';
import { FileUpload } from './FileUpload';

// Dynamic import of the FileUpload
const DynamicFileUpload = dynamic(
  () => import('./FileUpload').then((mod) => ({ default: mod.FileUpload })),
  {
    ssr: false, // Disable SSR for better performance
  }
);

// Export with proper TypeScript support
export type DynamicFileUploadProps = ComponentProps<typeof FileUpload>;
export type { UploadedFile } from './FileUpload';

export { DynamicFileUpload };
export default DynamicFileUpload;
