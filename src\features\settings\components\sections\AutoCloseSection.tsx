'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { AutoCloseConfigurationForm } from '../AutoCloseForm';

export function AutoCloseSection() {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>Auto-Close Timer</h2>
        <p className='text-sm text-muted-foreground'>
          Configure automatic ticket closure settings for resolved tickets.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Auto-Close Configuration</CardTitle>
          <CardDescription>
            Set how long resolved tickets remain open before being automatically
            closed. This helps maintain a clean ticket system while giving users
            time to respond.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AutoCloseConfigurationForm />
        </CardContent>
      </Card>
    </div>
  );
}
