/**
 * Modern React Query Settings Hooks - 2025 Optimized
 *
 * Replaces legacy Zustand server state with React Query patterns
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { settingsQueryOptions } from '@/lib/query-options';
import { useClerkSupabaseSync } from '@/hooks/useClerkSupabaseSync';
import { getDomainFromWindow } from '@/lib/domain';

/**
 * Hook for fetching all settings (user + admin)
 * Replaces the server state portion of the legacy settings store
 * Waits for sync completion to prevent race conditions
 */
export const useSettings = (
  tenantId: string,
  userId: string,
  enabled = true
) => {
  // Get sync status to prevent race conditions
  const syncTenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;
  const syncStatus = useClerkSupabaseSync(syncTenantId);

  return useQuery({
    ...settingsQueryOptions.all(tenantId, userId),
    enabled:
      enabled &&
      !!tenantId &&
      !!userId &&
      syncStatus.isComplete &&
      !syncStatus.isLoading,
  });
};

/**
 * Hook for updating user settings with optimistic updates
 */
export const useUpdateUserSettings = (tenantId: string, userId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: async (updates: any) => {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_settings: updates }),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update user settings: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate settings query to refetch fresh data
      queryClient.invalidateQueries({
        queryKey: ['settings', tenantId, userId],
      });
    },
  });
};

/**
 * Hook for updating admin settings with optimistic updates
 */
export const useUpdateAdminSettings = (tenantId: string, userId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: async (updates: any) => {
      const response = await fetch('/api/settings/admin', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update admin settings: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate settings query to refetch fresh data
      queryClient.invalidateQueries({
        queryKey: ['settings', tenantId, userId],
      });
    },
  });
};
