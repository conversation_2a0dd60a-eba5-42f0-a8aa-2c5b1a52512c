import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { z } from 'zod';

// Simplified validation schema
const AutoCloseConfigSchema = z.object({
  auto_close_enabled: z.boolean(),
  auto_close_days: z.number().min(1).max(365),
  auto_close_unit: z.enum(['hours', 'days', 'weeks']),
});

// Simplified user validation
async function validateUser(userId: string) {
  const serviceClient = createServiceSupabaseClient();
  const { data: userData, error } = await serviceClient
    .from('users')
    .select('id, tenant_id, role')
    .eq('clerk_id', userId)
    .single();

  if (error || !userData || !['admin', 'super_admin'].includes(userData.role)) {
    throw new Error('Unauthorized');
  }

  return userData;
}

// GET - Fetch auto-close configuration
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = await validateUser(userId);
    const serviceClient = createServiceSupabaseClient();

    // Fetch auto-close configuration for the tenant
    const { data: config, error } = await serviceClient
      .from('auto_close_configurations')
      .select('auto_close_enabled, auto_close_days, auto_close_unit')
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching auto-close configuration:', error);
      return NextResponse.json(
        { error: 'Failed to fetch configuration' },
        { status: 500 }
      );
    }

    // Return default configuration if none exists
    if (!config) {
      return NextResponse.json({
        auto_close_enabled: true,
        auto_close_days: 7,
        auto_close_unit: 'days',
      });
    }

    return NextResponse.json({
      auto_close_enabled: config.auto_close_enabled,
      auto_close_days: config.auto_close_days,
      auto_close_unit: config.auto_close_unit,
    });
  } catch (error) {
    console.error('Auto-close configuration GET error:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      {
        status:
          error instanceof Error && error.message === 'Insufficient permissions'
            ? 403
            : 500,
      }
    );
  }
}

// POST - Create or update auto-close configuration
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validation = AutoCloseConfigSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid configuration data',
          details: validation.error.issues,
        },
        { status: 400 }
      );
    }

    const currentUser = await validateUser(userId);
    const serviceClient = createServiceSupabaseClient();

    // Upsert configuration (simpler than checking existence)
    const { data: config, error } = await serviceClient
      .from('auto_close_configurations')
      .upsert({
        tenant_id: currentUser.tenant_id,
        auto_close_enabled: validation.data.auto_close_enabled,
        auto_close_days: validation.data.auto_close_days,
        auto_close_unit: validation.data.auto_close_unit,
        created_by: currentUser.id,
        updated_by: currentUser.id,
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving auto-close configuration:', error);
      return NextResponse.json(
        { error: 'Failed to save configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Auto-close configuration saved successfully',
      config,
    });
  } catch (error) {
    console.error('Auto-close configuration POST error:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      {
        status:
          error instanceof Error && error.message === 'Insufficient permissions'
            ? 403
            : 500,
      }
    );
  }
}

// DELETE - Reset to default configuration
export async function DELETE() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = await validateUser(userId);
    const serviceClient = createServiceSupabaseClient();

    // Reset to default configuration
    const { error } = await serviceClient
      .from('auto_close_configurations')
      .upsert({
        tenant_id: currentUser.tenant_id,
        auto_close_enabled: true,
        auto_close_days: 7,
        auto_close_unit: 'days',
        created_by: currentUser.id,
        updated_by: currentUser.id,
      });

    if (error) {
      console.error('Error resetting auto-close configuration:', error);
      return NextResponse.json(
        { error: 'Failed to reset configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Auto-close configuration reset to defaults (1 week)',
    });
  } catch (error) {
    console.error('Auto-close configuration DELETE error:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
      },
      {
        status:
          error instanceof Error && error.message === 'Insufficient permissions'
            ? 403
            : 500,
      }
    );
  }
}
