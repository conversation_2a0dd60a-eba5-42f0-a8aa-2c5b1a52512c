'use client';

import { cn } from '@/lib/utils';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

/**
 * Base Skeleton component following ShadCN UI patterns
 * Provides animated loading placeholders with accessibility support
 */
function Skeleton({ className, ...props }: SkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse rounded-md bg-gray-200 dark:bg-gray-700',
        className
      )}
      role='status'
      aria-label='Loading...'
      {...props}
    />
  );
}

/**
 * Skeleton variants for common use cases
 */
const SkeletonText = ({
  lines = 1,
  className,
  ...props
}: {
  lines?: number;
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('space-y-2', className)} {...props}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton
        key={i}
        className={cn('h-4', i === lines - 1 && lines > 1 ? 'w-3/4' : 'w-full')}
      />
    ))}
  </div>
);

const SkeletonAvatar = ({
  size = 'default',
  className,
  ...props
}: {
  size?: 'sm' | 'default' | 'lg';
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    default: 'h-10 w-10',
    lg: 'h-12 w-12',
  };

  return (
    <Skeleton
      className={cn('rounded-full', sizeClasses[size], className)}
      {...props}
    />
  );
};

const SkeletonButton = ({
  size = 'default',
  className,
  ...props
}: {
  size?: 'sm' | 'default' | 'lg';
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const sizeClasses = {
    sm: 'h-8 px-3',
    default: 'h-10 px-4',
    lg: 'h-12 px-6',
  };

  return (
    <Skeleton
      className={cn('rounded-md', sizeClasses[size], className)}
      {...props}
    />
  );
};

const SkeletonBadge = ({
  className,
  ...props
}: {
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>) => (
  <Skeleton className={cn('h-5 w-16 rounded-full', className)} {...props} />
);

export {
  Skeleton,
  SkeletonText,
  SkeletonAvatar,
  SkeletonButton,
  SkeletonBadge,
};
