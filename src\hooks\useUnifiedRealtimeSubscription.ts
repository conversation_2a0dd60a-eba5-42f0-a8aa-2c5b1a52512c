/**
 * Unified Real-time Subscription Manager - 2025 Centralized Pattern
 *
 * Single source of truth for all real-time events in the ticketing application.
 * Implements singleton pattern to ensure only one active subscription per tenant.
 *
 * Key Features:
 * - Single channel per tenant for all real-time events
 * - Smart cache updates for all related React Query caches
 * - Proper connection lifecycle management
 * - Zero complexity with minimal code approach
 *
 * <AUTHOR> Augster
 * @version 1.0 - Centralized Subscription Manager (January 2025)
 */

import { useEffect, useMemo, useRef } from 'react';
import { useQueryClient, QueryClient } from '@tanstack/react-query';
import type {
  RealtimePostgresChangesPayload,
  RealtimeChannel,
  SupabaseClient,
} from '@supabase/supabase-js';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import type { Database } from '@/types/supabase';
import RealtimeDataService from '@/lib/services/realtime-data.service';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { useTenantUuid } from '@/hooks/useRealtimeQuery';
import { QueryKeys } from '@/lib/query-keys';

// Type definitions for database rows
type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];
type UserRow = Database['public']['Tables']['users']['Row'];

// Type for items with id property
interface ItemWithId {
  id: string;
  [key: string]: unknown;
}

// Type for ticket data with common fields
interface TicketData extends ItemWithId {
  status?: string;
  priority?: string;
  department?: string;
  assignedTo?: string;
  updatedAt?: string | Date;
  title?: string;
  description?: string;
}

// Type for message data with common fields
interface MessageData extends ItemWithId {
  content?: string;
  updatedAt?: string | Date;
  isEdited?: boolean;
  attachments?: unknown[];
}

// Global connection registry to prevent any duplicate connections
const globalConnectionRegistry = new Map<string, boolean>();

// 2025 Enhanced Singleton Subscription Manager with Connection Optimization
class UnifiedSubscriptionManager {
  private static instances = new Map<string, UnifiedSubscriptionManager>();
  private channel: RealtimeChannel | null = null;
  private subscribers = new Set<string>();
  private tenantUuid: string;
  private supabase: SupabaseClient<Database>;
  private queryClient: QueryClient;
  private realtimeDataService: RealtimeDataService;
  private userDatabaseId: string | null;
  private isCreatingSubscription = false;
  private tenantSubdomain: string | null = null;

  // 2025 Connection Optimization Features
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionMonitor: NodeJS.Timeout | null = null;
  private fallbackPolling: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = Date.now();
  private connectionRetryCount = 0;
  private maxRetries = 5;
  private isConnectionHealthy = true;
  private fallbackActive = false;

  private constructor(
    tenantUuid: string,
    supabase: SupabaseClient<Database>,
    queryClient: QueryClient,
    realtimeDataService: RealtimeDataService,
    userDatabaseId: string | null
  ) {
    this.tenantUuid = tenantUuid;
    this.supabase = supabase;
    this.queryClient = queryClient;
    this.realtimeDataService = realtimeDataService;
    this.userDatabaseId = userDatabaseId;

    // Initialize tenant subdomain lookup
    this.initializeTenantSubdomain();
  }

  // Get tenant subdomain from UUID for proper cache key matching
  private async initializeTenantSubdomain(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('tenants')
        .select('subdomain')
        .eq('id', this.tenantUuid)
        .single();

      if (!error && data) {
        this.tenantSubdomain = data.subdomain;
        console.log(
          `🔍 Resolved tenant subdomain: ${this.tenantSubdomain} for UUID: ${this.tenantUuid}`
        );
      }
    } catch (error) {
      console.warn('Failed to resolve tenant subdomain:', error);
    }
  }

  static getInstance(
    tenantUuid: string,
    supabase: SupabaseClient<Database>,
    queryClient: QueryClient,
    realtimeDataService: RealtimeDataService,
    userDatabaseId: string | null
  ): UnifiedSubscriptionManager {
    // ROBUST SINGLETON: Only create new instance if none exists
    if (this.instances.has(tenantUuid)) {
      const existingInstance = this.instances.get(tenantUuid)!;
      console.log(
        `♻️ Reusing existing subscription for tenant ${tenantUuid} @ ${window.location.href}`
      );
      return existingInstance;
    }

    console.log(
      `🔗 Creating new subscription for tenant ${tenantUuid} @ ${window.location.href}`
    );

    const instance = new UnifiedSubscriptionManager(
      tenantUuid,
      supabase,
      queryClient,
      realtimeDataService,
      userDatabaseId
    );

    this.instances.set(tenantUuid, instance);
    return instance;
  }

  addSubscriber(subscriberId: string): void {
    this.subscribers.add(subscriberId);

    console.log(
      `🔗 Added subscriber ${subscriberId} to tenant ${this.tenantUuid}. Total subscribers: ${this.subscribers.size}`
    );

    if (
      this.subscribers.size === 1 &&
      !this.channel &&
      !this.isCreatingSubscription
    ) {
      console.log(
        `🚀 First subscriber for tenant ${this.tenantUuid} - creating subscription`
      );
      this.createSubscription();
    } else if (this.channel) {
      console.log(
        `♻️ Reusing existing subscription for tenant ${this.tenantUuid}`
      );
    } else if (this.isCreatingSubscription) {
      console.log(
        `⏳ Subscription already being created for tenant ${this.tenantUuid} - waiting`
      );
    }
  }

  removeSubscriber(subscriberId: string): void {
    this.subscribers.delete(subscriberId);

    console.log(
      `🔌 Removed subscriber ${subscriberId} from tenant ${this.tenantUuid}. Remaining subscribers: ${this.subscribers.size}`
    );

    if (this.subscribers.size === 0 && this.channel) {
      console.log(
        `🛑 No more subscribers for tenant ${this.tenantUuid} - destroying subscription`
      );
      this.destroySubscription();
    }
  }

  private createSubscription(): void {
    if (
      this.isCreatingSubscription ||
      this.channel ||
      globalConnectionRegistry.get(this.tenantUuid)
    ) {
      console.log(
        `🚫 Subscription already exists or being created for tenant ${this.tenantUuid}`
      );
      return;
    }

    this.isCreatingSubscription = true;
    globalConnectionRegistry.set(this.tenantUuid, true);

    const channelName = `unified-realtime-${this.tenantUuid}`;
    console.log(
      `🔄 Creating unified real-time subscription for tenant: ${this.tenantUuid}`
    );
    console.log(`📡 Channel name: ${channelName}`);
    console.log(`👥 Subscriber count: ${this.subscribers.size}`);

    this.channel = this.supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${this.tenantUuid}`,
        },
        (payload: RealtimePostgresChangesPayload<TicketRow>) =>
          this.handleTicketEvent(payload)
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_messages',
          filter: `tenant_id=eq.${this.tenantUuid}`,
        },
        (payload: RealtimePostgresChangesPayload<MessageRow>) =>
          this.handleMessageEvent(payload)
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `tenant_id=eq.${this.tenantUuid}`,
        },
        (payload: RealtimePostgresChangesPayload<UserRow>) =>
          this.handleUserEvent(payload)
      )
      .on('system', {}, (payload) => this.handleSystemEvent(payload))
      .subscribe((status) => this.handleSubscriptionStatus(status));

    this.isCreatingSubscription = false;
    console.log(
      `✅ Subscription created successfully for tenant ${this.tenantUuid}`
    );

    // 2025 Optimization: Start connection health monitoring
    this.startConnectionMonitoring();
    this.startSimpleFallback();
  }

  private destroySubscription(): void {
    if (this.channel) {
      console.log(
        '🔄 Destroying unified real-time subscription for tenant:',
        this.tenantUuid
      );
      this.supabase.removeChannel(this.channel);
      this.channel = null;
    }

    // 2025 Optimization: Clean up monitoring intervals
    this.stopConnectionMonitoring();
    this.stopSimpleFallback();

    this.isCreatingSubscription = false;
    globalConnectionRegistry.delete(this.tenantUuid);
  }

  private async handleTicketEvent(
    payload: RealtimePostgresChangesPayload<TicketRow>
  ): Promise<void> {
    try {
      const { eventType, new: newRow, old: oldRow } = payload;

      // CRITICAL FIX: Never skip ticket events - all users need to see status changes
      // This ensures proper synchronization for the user who made the change

      console.log(
        '🎫 Ticket event received:',
        eventType,
        newRow && typeof newRow === 'object' && 'id' in newRow
          ? newRow.id
          : 'unknown'
      );

      if (eventType === 'DELETE' && oldRow && 'id' in oldRow) {
        // Handle ticket deletion with proper React Query patterns
        await this.handleTicketDeletion(oldRow.id);
        return;
      }

      if (!newRow || !('id' in newRow)) return;

      // CRITICAL SECURITY: Transform ticket data using RealtimeDataService with error handling
      try {
        const transformedTicket =
          await this.realtimeDataService.transformTicketRow(
            newRow as TicketRow
          );

        // Use React Query optimistic updates for real-time synchronization
        await this.optimisticTicketUpdate(
          newRow.id,
          transformedTicket,
          eventType
        );
      } catch (transformError) {
        console.warn(
          `⚠️ SECURITY: Skipping ticket real-time update due to transformation error:`,
          newRow.id,
          transformError
        );

        // CRITICAL: Instead of corrupting cache, trigger background sync to get fresh data
        // This preserves existing good data while ensuring eventual consistency
        await this.backgroundSyncTicketData(newRow.id);
        return;
      }
    } catch (error) {
      console.error('Error handling ticket event:', error);
    }
  }

  // 2025 React Query Best Practice: Proper ticket deletion handling
  private async handleTicketDeletion(ticketId: string): Promise<void> {
    console.log(`🗑️ Handling ticket deletion: ${ticketId}`);

    // Use React Query's optimistic update pattern for deletion
    this.queryClient.setQueriesData(
      { queryKey: QueryKeys.TICKETS.all(this.tenantUuid) },
      (oldData: unknown) => {
        if (!Array.isArray(oldData)) return oldData;
        return oldData.filter((ticket: ItemWithId) => ticket.id !== ticketId);
      }
    );

    // Clean invalidation for related queries
    await this.queryClient.invalidateQueries({
      queryKey: QueryKeys.TICKETS.detail(this.tenantUuid, ticketId),
    });
  }

  // 2025 React Query Best Practice: Optimistic updates for real-time events
  private async optimisticTicketUpdate(
    ticketId: string,
    transformedTicket: unknown,
    eventType: string
  ): Promise<void> {
    console.log(
      `🎫 CRITICAL: Optimistic update for ticket: ${ticketId} (${eventType}) - ensuring all users see status changes`
    );

    // CRITICAL FIX: Validate transformed ticket has proper user data before cache updates
    const ticketData = transformedTicket as TicketData & {
      userName?: string;
      userEmail?: string;
      userAvatar?: string;
    };

    // SECURITY CHECK: Skip cache updates if user data is missing or corrupted
    if (
      !ticketData ||
      !ticketData.userName ||
      ticketData.userName.includes('Unknown') ||
      ticketData.userName.includes('anonymous') ||
      !ticketData.userEmail ||
      ticketData.userEmail.includes('unknown@') ||
      ticketData.userEmail.includes('temp.local')
    ) {
      console.warn(
        `⚠️ SECURITY: Skipping cache update for ticket ${ticketId} - corrupted user data detected`,
        'userName:',
        ticketData?.userName,
        'userEmail:',
        ticketData?.userEmail
      );

      // Instead of corrupting cache, just trigger background sync to get fresh data
      await this.backgroundSyncTicketData(ticketId);
      return;
    }

    // CRITICAL FIX: Update caches using BOTH tenantUuid AND tenantSubdomain
    // This ensures status changes are visible to ALL users, including the sender
    const tenantSubdomain = this.tenantSubdomain;

    // Update all ticket list caches using query patterns (both UUID and subdomain)
    this.queryClient.setQueriesData(
      { queryKey: QueryKeys.TICKETS.all(this.tenantUuid) },
      (oldData: unknown) => {
        if (!Array.isArray(oldData)) return oldData;
        return this.updateTicketInArray(
          oldData,
          ticketId,
          transformedTicket,
          eventType
        );
      }
    );

    // CRITICAL: Also update subdomain-based caches (what components actually use)
    if (tenantSubdomain && tenantSubdomain !== this.tenantUuid) {
      this.queryClient.setQueriesData(
        { queryKey: QueryKeys.TICKETS.all(tenantSubdomain) },
        (oldData: unknown) => {
          if (!Array.isArray(oldData)) return oldData;
          return this.updateTicketInArray(
            oldData,
            ticketId,
            transformedTicket,
            eventType
          );
        }
      );

      // Update specific ticket detail cache (subdomain-based) - with validation
      console.log(
        `🔄 CRITICAL: Updating detail cache with subdomain key: ${tenantSubdomain}`
      );
      this.queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantSubdomain, ticketId),
        transformedTicket
      );
      console.log(
        `✅ CRITICAL: Detail cache updated with subdomain key: ${tenantSubdomain}`
      );
    }

    // Update specific ticket detail cache (UUID-based)
    this.queryClient.setQueryData(
      QueryKeys.TICKETS.detail(this.tenantUuid, ticketId),
      transformedTicket
    );

    // Background sync to ensure data consistency
    await this.backgroundSyncTicketData(ticketId);
  }

  // Helper method to update ticket in array with smart field-level updates
  private updateTicketInArray(
    oldData: unknown[],
    ticketId: string,
    transformedTicket: unknown,
    eventType: string
  ): unknown[] {
    switch (eventType) {
      case 'INSERT': {
        // CRITICAL FIX: Check for existing ticket (optimistic or real) before inserting
        // This prevents duplicate keys when real-time events arrive after optimistic updates
        const existingIndex = oldData.findIndex((ticket: unknown) => {
          const typedTicket = ticket as ItemWithId;
          return typedTicket.id === ticketId;
        });

        if (existingIndex !== -1) {
          // Ticket already exists (likely optimistic), smart merge with real data
          const newData = [...oldData];
          const existingTicket = newData[existingIndex] as TicketData;
          newData[existingIndex] = this.smartMergeTicket(
            existingTicket,
            transformedTicket
          );
          console.log(
            `🔄 Smart merged existing ticket at index ${existingIndex} for ID: ${ticketId}`
          );
          return newData;
        } else {
          // New ticket, add to beginning
          console.log(`➕ Added new ticket to beginning for ID: ${ticketId}`);
          return [transformedTicket, ...oldData];
        }
      }
      case 'UPDATE':
        return oldData.map((ticket: unknown) => {
          const typedTicket = ticket as ItemWithId;
          if (typedTicket.id === ticketId) {
            // Smart merge - only update changed fields
            return this.smartMergeTicket(ticket, transformedTicket);
          }
          return ticket;
        });
      default:
        return oldData;
    }
  }

  // 2025 Smart Cache: Only update fields that actually changed, PROTECT user identity fields
  private smartMergeTicket(existing: unknown, incoming: unknown): unknown {
    const existingTicket = existing as TicketData & {
      userName?: string;
      userEmail?: string;
      userAvatar?: string;
    };
    const incomingTicket = incoming as TicketData & {
      userName?: string;
      userEmail?: string;
      userAvatar?: string;
    };
    const merged = { ...existingTicket };
    const changedFields: string[] = [];

    // CRITICAL SECURITY: Protected user identity fields - NEVER overwrite with Unknown/anonymous data
    const protectedUserFields = ['userName', 'userEmail', 'userAvatar'];
    const hasCorruptedUserData =
      incomingTicket.userName?.includes('Unknown') ||
      incomingTicket.userName?.includes('anonymous') ||
      incomingTicket.userEmail?.includes('unknown@') ||
      incomingTicket.userEmail?.includes('temp.local');

    if (hasCorruptedUserData) {
      console.warn(
        `🛡️ SECURITY: Protecting user identity fields from corruption for ticket ${incomingTicket.id}`,
        'Corrupted incoming data:',
        {
          userName: incomingTicket.userName,
          userEmail: incomingTicket.userEmail,
        },
        'Preserving existing data:',
        {
          userName: existingTicket.userName,
          userEmail: existingTicket.userEmail,
        }
      );
    }

    // Check key fields that commonly change (excluding protected user fields)
    const fieldsToCheck = [
      'status',
      'priority',
      'department',
      'assignedTo',
      'updatedAt',
      'title',
      'description',
    ];

    fieldsToCheck.forEach((field) => {
      const oldValue = existingTicket[field];
      const newValue = incomingTicket[field];

      // Simple equality check (handles most cases)
      if (oldValue !== newValue) {
        merged[field] = newValue;
        changedFields.push(field);
      }
    });

    // CRITICAL SECURITY: Only update user identity fields if incoming data is valid
    protectedUserFields.forEach((field) => {
      const oldValue = existingTicket[field];
      const newValue = incomingTicket[field];

      // Only update if new value is valid and different
      if (oldValue !== newValue && newValue && !hasCorruptedUserData) {
        merged[field] = newValue;
        changedFields.push(field);
      } else if (hasCorruptedUserData) {
        // Explicitly preserve existing good data
        merged[field] = oldValue;
      }
    });

    if (changedFields.length > 0) {
      console.log(
        `🔄 Smart merge updated fields: ${changedFields.join(', ')} for ticket ${incomingTicket.id}`
      );
    } else {
      console.log(
        `✨ No field changes detected for ticket ${incomingTicket.id}`
      );
    }

    return merged;
  }

  // 2025 React Query Best Practice: Clean background synchronization
  private async backgroundSyncTicketData(ticketId: string): Promise<void> {
    try {
      console.log(`🔄 Background sync for ticket: ${ticketId}`);

      // Use React Query's built-in background refetch
      await this.queryClient.refetchQueries({
        queryKey: QueryKeys.TICKETS.detail(this.tenantUuid, ticketId),
        type: 'active', // Only refetch if query is currently active
      });

      console.log(`✅ Background sync completed for ticket: ${ticketId}`);
    } catch (error) {
      console.error(`❌ Background sync failed for ticket ${ticketId}:`, error);
    }
  }

  // 2025 React Query Best Practice: Optimistic message updates for real-time events
  private async optimisticMessageUpdate(
    ticketId: string,
    payload: RealtimePostgresChangesPayload<MessageRow>,
    eventType: string
  ): Promise<void> {
    try {
      const { new: newRow, old: oldRow } = payload;
      const tenantSubdomain = this.tenantSubdomain;

      console.log(
        `💬 Optimistic message update for ticket: ${ticketId} (${eventType})`
      );

      if (eventType === 'INSERT' && newRow) {
        // Transform message data using RealtimeDataService
        const transformedMessage =
          await this.realtimeDataService.transformMessageRow(
            newRow as MessageRow
          );

        // Update message caches using BOTH UUID and subdomain keys
        this.updateMessageCaches(ticketId, transformedMessage, 'INSERT');

        // CRITICAL: Also update subdomain-based caches (what components actually use)
        if (tenantSubdomain && tenantSubdomain !== this.tenantUuid) {
          console.log(
            `💬 CRITICAL: Updating message cache with subdomain key: ${tenantSubdomain}`
          );
          this.queryClient.setQueryData(
            QueryKeys.TICKETS.messages(tenantSubdomain, ticketId),
            (oldData: unknown[] | undefined) => {
              if (!Array.isArray(oldData)) return [transformedMessage];
              return [...oldData, transformedMessage];
            }
          );
          console.log(
            `✅ CRITICAL: Message cache updated with subdomain key: ${tenantSubdomain}`
          );
        }
      } else if (eventType === 'UPDATE' && newRow) {
        // Transform updated message data
        const transformedMessage =
          await this.realtimeDataService.transformMessageRow(
            newRow as MessageRow
          );

        // Update message caches for both UUID and subdomain
        this.updateMessageCaches(ticketId, transformedMessage, 'UPDATE');

        if (tenantSubdomain && tenantSubdomain !== this.tenantUuid) {
          this.queryClient.setQueryData(
            QueryKeys.TICKETS.messages(tenantSubdomain, ticketId),
            (oldData: unknown[] | undefined) => {
              if (!Array.isArray(oldData)) return [transformedMessage];
              return oldData.map((msg: unknown) => {
                const typedMsg = msg as { id: string };
                const messageRow = newRow as { id: string };
                return typedMsg.id === messageRow.id ? transformedMessage : msg;
              });
            }
          );
        }
      } else if (eventType === 'DELETE' && oldRow) {
        // Remove message from caches
        this.updateMessageCaches(ticketId, oldRow, 'DELETE');

        if (tenantSubdomain && tenantSubdomain !== this.tenantUuid) {
          this.queryClient.setQueryData(
            QueryKeys.TICKETS.messages(tenantSubdomain, ticketId),
            (oldData: unknown[] | undefined) => {
              if (!Array.isArray(oldData)) return [];
              return oldData.filter((msg: unknown) => {
                const typedMsg = msg as { id: string };
                const messageRow = oldRow as { id: string };
                return typedMsg.id !== messageRow.id;
              });
            }
          );
        }
      }

      console.log(
        `✅ Optimistic message update completed for ticket: ${ticketId}`
      );
    } catch (error) {
      console.error(
        `❌ Optimistic message update failed for ticket ${ticketId}:`,
        error
      );
    }
  }

  // Helper method to update message caches with smart incremental updates
  private updateMessageCaches(
    ticketId: string,
    messageData: unknown,
    eventType: string
  ): void {
    const messageQueryKey = QueryKeys.TICKETS.messages(
      this.tenantUuid,
      ticketId
    );

    switch (eventType) {
      case 'INSERT':
        this.queryClient.setQueryData(
          messageQueryKey,
          (oldData: unknown[] | undefined) => {
            if (!Array.isArray(oldData)) return [messageData];

            // Smart check: prevent duplicate messages
            const messageId = (messageData as MessageData)?.id;
            const exists = oldData.some(
              (msg: unknown) => (msg as MessageData).id === messageId
            );

            if (exists) {
              console.log(
                `✨ Message ${messageId} already exists, skipping duplicate`
              );
              return oldData;
            }

            console.log(
              `💬 Adding new message ${messageId} to ticket ${ticketId}`
            );
            return [...oldData, messageData];
          }
        );
        break;

      case 'UPDATE':
        this.queryClient.setQueryData(
          messageQueryKey,
          (oldData: unknown[] | undefined) => {
            if (!Array.isArray(oldData)) return [messageData];
            return oldData.map((msg: unknown) => {
              const typedMsg = msg as { id: string };
              const typedMessageData = messageData as { id: string };
              if (typedMsg.id === typedMessageData.id) {
                // Smart merge for messages too
                return this.smartMergeMessage(msg, messageData);
              }
              return msg;
            });
          }
        );
        break;

      case 'DELETE':
        this.queryClient.setQueryData(
          messageQueryKey,
          (oldData: unknown[] | undefined) => {
            if (!Array.isArray(oldData)) return [];
            const typedMessageData = messageData as { id: string };
            return oldData.filter((msg: unknown) => {
              const typedMsg = msg as { id: string };
              return typedMsg.id !== typedMessageData.id;
            });
          }
        );
        break;
    }
  }

  // 2025 Smart Cache: Smart message merging
  private smartMergeMessage(existing: unknown, incoming: unknown): unknown {
    const existingMsg = existing as MessageData;
    const incomingMsg = incoming as MessageData;
    const merged = { ...existingMsg };
    const changedFields: string[] = [];

    // Check key message fields that commonly change
    const fieldsToCheck = ['content', 'updatedAt', 'isEdited', 'attachments'];

    fieldsToCheck.forEach((field) => {
      const oldValue = existingMsg[field];
      const newValue = incomingMsg[field];

      if (oldValue !== newValue) {
        merged[field] = newValue;
        changedFields.push(field);
      }
    });

    if (changedFields.length > 0) {
      console.log(
        `💬 Smart merge updated message fields: ${changedFields.join(', ')} for message ${incomingMsg.id}`
      );
    }

    return merged;
  }

  private async handleMessageEvent(
    payload: RealtimePostgresChangesPayload<MessageRow>
  ): Promise<void> {
    try {
      const { eventType, new: newRow, old: oldRow } = payload;

      // Skip updates for current user's own actions to prevent duplicates
      if (
        this.userDatabaseId &&
        eventType === 'INSERT' &&
        newRow &&
        'author_id' in newRow
      ) {
        if (newRow.author_id === this.userDatabaseId) return;
      }

      console.log(
        '💬 Message event received:',
        eventType,
        newRow && 'id' in newRow ? newRow.id : 'unknown'
      );

      const ticketId =
        (newRow && 'ticket_id' in newRow ? newRow.ticket_id : null) ||
        (oldRow && 'ticket_id' in oldRow ? oldRow.ticket_id : null);
      if (!ticketId) return;

      // Use React Query optimistic updates for real-time message synchronization
      await this.optimisticMessageUpdate(ticketId, payload, eventType);

      // Background sync ticket data since messages can change status
      await this.backgroundSyncTicketData(ticketId);
    } catch (error) {
      console.error('Error handling message event:', error);
    }
  }

  private async handleUserEvent(
    payload: RealtimePostgresChangesPayload<UserRow>
  ): Promise<void> {
    try {
      const { eventType, new: newRow } = payload;

      console.log(
        '👤 User event received:',
        eventType,
        newRow && 'id' in newRow ? newRow.id : 'unknown'
      );

      if (!newRow || !('id' in newRow)) return;

      // User changes can affect ticket assignments and display
      await this.queryClient.invalidateQueries({
        queryKey: QueryKeys.USERS.all(this.tenantUuid),
      });

      // Invalidate all ticket caches since user data affects assignments and display
      await this.queryClient.invalidateQueries({
        queryKey: QueryKeys.TICKETS.all(this.tenantUuid),
      });

      // Legacy cache updates for backward compatibility
      this.updateUserCaches(/* newRow.id */);
      this.invalidateTicketCachesForUser(/* newRow.id */);
    } catch (error) {
      console.error('Error handling user event:', error);
    }
  }

  private updateUserCaches(/* userId: string */): void {
    // Invalidate user caches
    // Note: userId parameter reserved for future specific user cache invalidation
    this.queryClient.invalidateQueries({
      queryKey: QueryKeys.USERS.all(this.tenantUuid),
    });
  }

  private invalidateTicketCachesForUser(/* userId: string */): void {
    // Invalidate all ticket caches since user data affects assignments
    // Note: userId parameter reserved for future specific user-related ticket cache invalidation
    this.queryClient.invalidateQueries({
      queryKey: QueryKeys.TICKETS.all(this.tenantUuid),
    });
  }

  // 2025 Connection Optimization: Heartbeat mechanism to prevent connection sleep
  private startConnectionMonitoring(): void {
    // Heartbeat every 30 seconds to keep connection alive
    this.heartbeatInterval = setInterval(() => {
      if (this.channel && this.isConnectionHealthy) {
        this.channel.send({
          type: 'broadcast',
          event: 'heartbeat',
          payload: { timestamp: Date.now() },
        });
        this.lastHeartbeat = Date.now();
        console.log(`💓 Heartbeat sent for tenant ${this.tenantUuid}`);
      }
    }, 30000); // 30 seconds

    // Connection health monitor every 60 seconds
    this.connectionMonitor = setInterval(() => {
      this.checkConnectionHealth();
    }, 60000); // 1 minute

    console.log(
      `🔍 Connection monitoring started for tenant ${this.tenantUuid}`
    );
  }

  // 2025 Connection Optimization: Stop monitoring when subscription is destroyed
  private stopConnectionMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.connectionMonitor) {
      clearInterval(this.connectionMonitor);
      this.connectionMonitor = null;
    }

    console.log(
      `🛑 Connection monitoring stopped for tenant ${this.tenantUuid}`
    );
  }

  // 2025 Simple Fallback: Basic polling when real-time fails
  private startSimpleFallback(): void {
    this.fallbackPolling = setInterval(() => {
      if (!this.isConnectionHealthy && !this.fallbackActive) {
        console.log(
          `🔄 Starting simple fallback polling for tenant ${this.tenantUuid}`
        );
        this.fallbackActive = true;
        this.performFallbackSync();
      }
    }, 30000); // Check every 30 seconds
  }

  private stopSimpleFallback(): void {
    if (this.fallbackPolling) {
      clearInterval(this.fallbackPolling);
      this.fallbackPolling = null;
    }
    this.fallbackActive = false;
  }

  // Simple fallback sync with smart cache validation
  private async performFallbackSync(): Promise<void> {
    if (this.isConnectionHealthy) {
      this.fallbackActive = false;
      return;
    }

    try {
      console.log(`📡 Smart fallback sync for tenant ${this.tenantUuid}`);

      // Smart approach: only refresh stale data
      await this.smartCacheValidation();
    } catch (error) {
      console.error('Fallback sync failed:', error);
    }
  }

  // 2025 Smart Cache: Only update outdated data
  public async smartCacheValidation(): Promise<void> {
    const staleThreshold = 60000; // 1 minute
    const now = Date.now();

    // Get all queries for this tenant
    const allQueries = this.queryClient.getQueryCache().getAll();
    const tenantQueries = allQueries.filter((query) => {
      const key = query.queryKey;
      return (
        Array.isArray(key) &&
        (key.includes(this.tenantUuid) ||
          (this.tenantSubdomain && key.includes(this.tenantSubdomain)))
      );
    });

    for (const query of tenantQueries) {
      const age = now - (query.state.dataUpdatedAt || 0);

      // Only refresh if data is stale
      if (age > staleThreshold && query.state.data) {
        console.log(`🔄 Refreshing stale query:`, query.queryKey);

        // Silent background refresh without loading states
        if (query.options.queryFn) {
          this.queryClient
            .fetchQuery({
              queryKey: query.queryKey,
              queryFn: query.options.queryFn,
              staleTime: 0,
            })
            .catch(() => {
              // Fail silently to avoid disrupting UX
            });
        }
      }
    }
  }

  // 2025 Connection Optimization: Health check and auto-recovery
  private checkConnectionHealth(): void {
    const now = Date.now();
    const timeSinceLastHeartbeat = now - this.lastHeartbeat;
    const healthThreshold = 90000; // 90 seconds - more aggressive

    if (timeSinceLastHeartbeat > healthThreshold) {
      console.warn(
        `⚠️ Connection health degraded for tenant ${this.tenantUuid}`
      );
      this.isConnectionHealthy = false;
      this.attemptConnectionRecovery();
    } else {
      this.isConnectionHealthy = true;
      this.connectionRetryCount = 0; // Reset retry count on healthy connection
      if (this.fallbackActive) {
        console.log(
          `✅ Connection recovered, stopping fallback for tenant ${this.tenantUuid}`
        );
        this.fallbackActive = false;
      }
    }
  }

  // 2025 Connection Optimization: Smart reconnection with exponential backoff
  private attemptConnectionRecovery(): void {
    if (this.connectionRetryCount >= this.maxRetries) {
      console.error(
        `❌ Max reconnection attempts reached for tenant ${this.tenantUuid}`
      );
      return;
    }

    this.connectionRetryCount++;
    const backoffDelay = Math.min(
      1000 * Math.pow(2, this.connectionRetryCount),
      30000
    );

    console.log(
      `🔄 Attempting connection recovery for tenant ${this.tenantUuid} (attempt ${this.connectionRetryCount}/${this.maxRetries})`
    );

    setTimeout(() => {
      try {
        if (this.channel) {
          this.supabase.removeChannel(this.channel);
          this.channel = null;
        }

        // Reset flags and recreate subscription
        this.isCreatingSubscription = false;
        globalConnectionRegistry.delete(this.tenantUuid);
        this.createSubscription();
      } catch (error) {
        console.error(`❌ Error during connection recovery:`, error);
        // Reset retry count and try again after a longer delay
        this.connectionRetryCount = Math.max(0, this.connectionRetryCount - 1);
        setTimeout(() => this.attemptConnectionRecovery(), 5000);
      }
    }, backoffDelay);
  }

  // 2025 Connection Optimization: Handle system events for connection monitoring
  private handleSystemEvent(payload: {
    type?: string;
    [key: string]: unknown;
  }): void {
    console.log(`🔧 System event for tenant ${this.tenantUuid}:`, payload);

    if (payload.type === 'pong') {
      this.lastHeartbeat = Date.now();
      this.isConnectionHealthy = true;
      console.log(`💓 Pong received for tenant ${this.tenantUuid}`);
    }
  }

  // 2025 Connection Optimization: Handle subscription status changes
  private handleSubscriptionStatus(status: string): void {
    console.log(
      `📡 Subscription status for tenant ${this.tenantUuid}: ${status}`
    );

    switch (status) {
      case 'SUBSCRIBED':
        this.isConnectionHealthy = true;
        this.connectionRetryCount = 0;
        this.lastHeartbeat = Date.now();
        if (this.fallbackActive) {
          console.log(
            `✅ Real-time reconnected, stopping fallback for tenant ${this.tenantUuid}`
          );
          this.fallbackActive = false;
        }
        break;
      case 'CHANNEL_ERROR':
      case 'TIMED_OUT':
        this.isConnectionHealthy = false;
        this.attemptConnectionRecovery();
        break;
      case 'CLOSED':
        this.isConnectionHealthy = false;
        break;
    }
  }

  // Instance cleanup method for immediate cleanup
  cleanup(): void {
    console.log(`🧹 CLEANUP: Starting cleanup for tenant ${this.tenantUuid}`);

    // Clear all subscribers
    this.subscribers.clear();

    // Destroy subscription immediately
    this.destroySubscription();

    console.log(`✅ CLEANUP: Cleanup completed for tenant ${this.tenantUuid}`);
  }

  static cleanup(tenantUuid: string): void {
    const instance = this.instances.get(tenantUuid);
    if (instance && instance.subscribers.size === 0) {
      instance.destroySubscription();
      this.instances.delete(tenantUuid);
    }
  }

  // Simple connection status check
  getConnectionStatus(): {
    isConnected: boolean;
    fallbackActive: boolean;
    lastHeartbeat: number;
  } {
    return {
      isConnected: this.isConnectionHealthy,
      fallbackActive: this.fallbackActive,
      lastHeartbeat: this.lastHeartbeat,
    };
  }

  static getConnectionStatus(tenantUuid: string): {
    isConnected: boolean;
    fallbackActive: boolean;
    lastHeartbeat: number;
  } | null {
    const instance = this.instances.get(tenantUuid);
    return instance ? instance.getConnectionStatus() : null;
  }
}

/**
 * Simple hook to get real-time connection status
 */
export function useRealtimeConnectionStatus(tenantId: string): {
  isConnected: boolean;
  fallbackActive: boolean;
  lastHeartbeat: number;
} | null {
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  if (!tenantUuid) return null;

  return UnifiedSubscriptionManager.getConnectionStatus(tenantUuid);
}

/**
 * Simple hook for smart cache validation - triggers on page refresh
 */
export function useSmartCacheRefresh(tenantId: string): void {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { isLoggingOut } = useAuth();
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  useEffect(() => {
    if (!tenantUuid || !supabase || isLoggingOut) return;

    // On mount (page refresh), perform smart cache validation
    const performSmartRefresh = async () => {
      const manager = UnifiedSubscriptionManager.getInstance(
        tenantUuid,
        supabase,
        queryClient,
        new RealtimeDataService(supabase),
        null
      );

      // Use the public method for smart cache validation
      await manager.smartCacheValidation();
    };

    // Small delay to let initial queries settle
    const timeout = setTimeout(performSmartRefresh, 1000);

    return () => clearTimeout(timeout);
  }, [tenantUuid, supabase, queryClient, isLoggingOut]);
}

/**
 * Unified Real-time Subscription Hook
 *
 * Provides centralized real-time subscription management for all components.
 * Implements singleton pattern to ensure only one connection per tenant.
 */
export function useUnifiedRealtimeSubscription(tenantId: string): void {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { user, isLoggingOut } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // Resolve tenant UUID
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Initialize RealtimeDataService
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase),
    [supabase]
  );

  // Generate unique subscriber ID for this hook instance
  const subscriberIdRef = useRef<string>(
    `subscriber-${Date.now()}-${Math.random()}`
  );
  if (!subscriberIdRef.current) {
    subscriberIdRef.current = `subscriber-${Date.now()}-${Math.random()}`;
  }

  useEffect(() => {
    if (!supabase || !tenantUuid || !user || isLoggingOut) return;

    // Add a small delay to prevent rapid mount/unmount cycles
    const timeoutId = setTimeout(() => {
      const manager = UnifiedSubscriptionManager.getInstance(
        tenantUuid,
        supabase,
        queryClient,
        realtimeDataService,
        userDatabaseId
      );

      manager.addSubscriber(subscriberIdRef.current!);
    }, 50); // 50ms delay to debounce rapid mounts

    return () => {
      clearTimeout(timeoutId);

      // Only remove subscriber if we actually added one
      const manager = UnifiedSubscriptionManager.getInstance(
        tenantUuid,
        supabase,
        queryClient,
        realtimeDataService,
        userDatabaseId
      );

      manager.removeSubscriber(subscriberIdRef.current!);

      // Cleanup singleton if no more subscribers with longer delay
      setTimeout(() => {
        UnifiedSubscriptionManager.cleanup(tenantUuid);
      }, 2000); // Increased delay to 2 seconds for better stability
    };
  }, [
    supabase,
    tenantUuid,
    user,
    userDatabaseId,
    queryClient,
    realtimeDataService,
    isLoggingOut,
  ]);
}
