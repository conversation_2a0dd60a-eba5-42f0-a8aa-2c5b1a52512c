---
type: 'always_apply'
description: 'Example description'
---

# Guideline & Memory Enforcement Rule

- Strictly check and apply user guidelines & memories before any output. Mandatory.

# Security

- Backend Validation First: Handle all sensitive data exclusively on the server side.
- Strict Input Sanitization: Validate and sanitize every user input; never trust frontend-only validation.
- Dependency Vetting: Thoroughly assess security, maintenance, and reputation before integrating third-party libraries.
- Protect Secrets: Never expose credentials or secrets in source code. Use environment variables (`.env`) and secure secret management.

# ESLint & TypeScript Rules

- Avoid `any`: Use specific or `unknown` types to maintain strong type safety.
- Remove Unused Code: Eliminate unused variables and placeholders promptly.
- Explicit Return Types: Always specify return types for exported functions.
- Leverage Type Inference: Avoid redundant typing where TypeScript can infer types correctly.
- No `console.log`: Use proper logging frameworks or remove debug logs before production.
- JSX Lists: Ensure list elements have unique and stable `key` props.
- Hook Dependencies: Maintain precise dependency arrays in React hooks (`useEffect`, `useCallback`).
- No `@ts-ignore`: Fix type errors properly; do not silence them.
- No Empty Functions: If intentional, add clear explanatory comments.
- Declare Before Use: Always import or declare variables before referencing them.
- No Non-Null Assertions (`!`): Check for `null` or `undefined` before accessing values.
- Prefer `const`: Use `const` unless reassignment is necessary.
- Strict Equality: Always use `===` and `!==`; avoid loose equality operators (`==`, `!=`).
- No Variable Shadowing: Avoid reusing variable names in nested or inner scopes.

# Code & UI Standards

- Design Principles: Follow YAGNI, SOLID, KISS, and DRY principles rigorously for maintainable design.
- Strict Typing: Enable TypeScript strict mode; supplement with JSDoc comments where clarity is required.
- Clean Code: Write modular, readable, and maintainable code with clear separation of concerns.
- File Size: Split files exceeding 300 lines into smaller, focused modules.
- Code Reuse: Avoid duplication by extracting reusable components and utilities.
- Optimize Pragmatically: Prioritize code clarity over premature optimization.
- Formatting: Adhere strictly to ESLint and Prettier rules for consistent formatting.
- Meaningful Naming: Use descriptive, consistent naming conventions; avoid temporary or vague names.
- No Throwaway Code: Avoid committing throwaway or one-off scripts to the main repository.
- UI Library Compliance: Use ShadCN UI as the default UI framework and conform to its structure and styling guidelines.

# Prohibited Practices

- No Navigation Hacks: Avoid forced redirects or bypassing routing and authentication flows.
- No Mock Data in Production: Limit mock or stub data strictly to development or test environments only.
- Follow 2025 Best Practices: Use modern, secure, scalable methods; keep dependencies updated and aligned with official standards.

# Core Philosophy

- Simplicity: Favor clear, maintainable code over clever or unnecessarily complex solutions.
- Iterative Improvement: Refactor and enhance working code instead of full rewrites unless absolutely necessary.
- Focus: Stay aligned with task scope and avoid scope creep.
- Quality: Deliver clean, tested, and secure code consistently.
- Collaboration: Maintain transparent communication, document decisions, and align closely with team members.

# Servers

- No Duplicate Servers: Never run `npm run dev` if an existing server instance is active. Always terminate running servers first.
- Verify Processes: Use `list-processes` to identify active server instances before restarting.
- Targeted Restart: Restart servers using exact terminal/process IDs only.
- Restart Workflow: Follow this sequence precisely — `list-processes` → `kill-process` → `npm run dev`.
- Port Management: Restrict to existing ports only; do not open new ports without explicit authorization.
