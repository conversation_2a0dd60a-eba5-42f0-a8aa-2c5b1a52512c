'use client';

import { useTheme } from 'next-themes';
import { Toaster as Sonner, ToasterProps } from 'sonner';
import { NoSSR } from './no-ssr';

const ToasterInner = ({ ...props }: ToasterProps) => {
  const { theme } = useTheme();

  // Ensure theme is never undefined for exactOptionalPropertyTypes compatibility
  const resolvedTheme: 'light' | 'dark' | 'system' =
    theme === 'light' || theme === 'dark' || theme === 'system'
      ? theme
      : 'system';

  return <Sonner theme={resolvedTheme} className='toaster group' {...props} />;
};

const Toaster = (props: ToasterProps) => {
  return (
    <NoSSR>
      <ToasterInner {...props} />
    </NoSSR>
  );
};

export { Toaster };
