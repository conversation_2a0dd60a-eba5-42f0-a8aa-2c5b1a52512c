'use client';

import { memo, useMemo } from 'react';
import { useUser } from '@clerk/nextjs';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/features/shared/components/ui/avatar';
import { cn } from '@/lib/utils';

interface ProfileAvatarProps {
  /** User's avatar URL from database or Clerk */
  avatarUrl?: string | null;
  /** User's full name for fallback initials */
  name: string;
  /** User's email for additional fallback */
  email?: string;
  /** User's Clerk ID for direct Clerk image access */
  clerkId?: string;
  /** Avatar size className */
  className?: string;
  /** Fallback background color className */
  fallbackClassName?: string;
}

/**
 * Universal ProfileAvatar Component
 *
 * Displays user profile images with intelligent fallback logic:
 * 1. Database stored avatar_url (from Clerk sync)
 * 2. Direct Clerk image access for current user
 * 3. First name letter fallback
 *
 * Features:
 * - Cache-first loading with Radix Avatar
 * - Automatic fallback to initials
 * - Optimized for performance with memo
 * - Consistent styling across all components
 *
 * <AUTHOR> Augster
 * @version 1.0 - Universal Profile Avatar (January 2025)
 */
export const ProfileAvatar = memo<ProfileAvatarProps>(
  ({
    avatarUrl,
    name,
    email,
    clerkId,
    className = 'h-10 w-10',
    fallbackClassName = 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 font-medium text-sm',
  }) => {
    const { user: currentUser } = useUser();

    // Determine the best image source with priority order
    const imageSource = useMemo(() => {
      // 1. For current user, prioritize Clerk's live image URL (always fresh and valid)
      if (currentUser?.imageUrl) {
        const isCurrentUser =
          (clerkId && currentUser.id === clerkId) ||
          (email && currentUser.primaryEmailAddress?.emailAddress === email);

        if (isCurrentUser) {
          return currentUser.imageUrl;
        }
      }

      // 2. Use provided avatar URL (from database) for other users
      if (avatarUrl) {
        return avatarUrl;
      }

      // 3. No image available
      return null;
    }, [avatarUrl, clerkId, email, currentUser]);

    // Generate initials for fallback
    const initials = useMemo(() => {
      if (!name) return email?.[0]?.toUpperCase() || 'U';

      return (
        name
          .split(' ')
          .map((n) => n[0])
          .join('')
          .toUpperCase()
          .slice(0, 2) || 'U'
      );
    }, [name, email]);

    return (
      <Avatar
        className={cn('shrink-0 overflow-hidden rounded-full', className)}
      >
        {imageSource && (
          <AvatarImage
            src={imageSource}
            alt={`${name}'s profile picture`}
            className='aspect-square size-full object-cover'
          />
        )}
        <AvatarFallback className={cn(fallbackClassName)}>
          {initials}
        </AvatarFallback>
      </Avatar>
    );
  }
);

ProfileAvatar.displayName = 'ProfileAvatar';

/**
 * Helper function to get initials from name
 * @deprecated Use ProfileAvatar component instead
 */
export const getInitials = (name: string): string => {
  if (!name) return 'U';
  return (
    name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase() || 'U'
  );
};
