'use client';

import { useMemo, useCallback, memo } from 'react';

import { Label } from '@/features/shared/components/ui/label';
import { Switch } from '@/features/shared/components/ui/switch';
import { UserAutocomplete } from '@/features/shared/components/UserAutocomplete';
import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { useAdminSettings } from '../hooks/useSettingsSync';
import { useUsers } from '@/hooks/useUsers';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { User, AlertCircle } from 'lucide-react';
import { toast } from '@/features/shared/components/toast';
import { cn } from '@/lib/utils';

const DefaultAgentSelectorComponent = () => {
  const {
    adminSettings,
    assignDefaultAgent,
    removeDefaultAgent,
    toggleDefaultAgentStatus,
    isLoading,
  } = useAdminSettings();
  const { tenantId } = useAuth();
  // Derive state directly from store instead of using local state with useEffect
  // Extract complex expressions to separate variables for dependency arrays
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const defaultAgentSettings = (adminSettings as any)?.default_agent_settings;
  const defaultAgentId = defaultAgentSettings?.default_agent_id;
  const isActive = defaultAgentSettings?.is_active;

  const currentDefaultAgentId = useMemo(() => {
    return defaultAgentId || null;
  }, [defaultAgentId]);

  const currentIsActive = useMemo(() => {
    return isActive ?? true;
  }, [isActive]);

  // Use store state directly for display - no local state synchronization needed
  const displayAgentId = currentDefaultAgentId;

  const displayAgentIds = useMemo((): string[] => {
    return displayAgentId ? [displayAgentId] : [];
  }, [displayAgentId]);

  const { data: displayAgentDetails = [] } = useUsers(
    tenantId || '',
    displayAgentIds
  );

  const displayAgent = useMemo(() => {
    return displayAgentDetails[0] || null;
  }, [displayAgentDetails]);

  const handleAgentChange = useCallback(
    async (value: string | string[]) => {
      const newAgentId = Array.isArray(value)
        ? value[0] || null
        : value || null;

      if (!newAgentId) {
        // This shouldn't happen in normal flow, but handle gracefully
        return;
      }

      try {
        await assignDefaultAgent(newAgentId);

        // Get agent details for toast notification
        const agentDetails = displayAgentDetails?.[0];
        const agentName = agentDetails?.name || 'Selected agent';
        toast.success('Default Agent Assigned', {
          description: `${agentName} is now the default agent for new tickets`,
        });
      } catch {
        toast.error('Assignment Failed', {
          description: 'Unable to assign default agent. Please try again.',
        });
      }
    },
    [assignDefaultAgent, displayAgentDetails]
  );

  const handleRemoveSelected = useCallback(async () => {
    try {
      await removeDefaultAgent();
      toast.success('Default Agent Removed', {
        description:
          'New tickets will remain unassigned until manually assigned',
      });
    } catch {
      toast.error('Removal Failed', {
        description: 'Unable to remove default agent. Please try again.',
      });
    }
  }, [removeDefaultAgent]);

  const handleToggleActive = useCallback(async () => {
    const newActiveState = !currentIsActive;

    try {
      await toggleDefaultAgentStatus(newActiveState);
      toast.success(
        newActiveState
          ? 'Default Agent Activated'
          : 'Default Agent Deactivated',
        {
          description: newActiveState
            ? 'New tickets will be automatically assigned to the default agent'
            : 'Agent will remain assigned but inactive for new tickets',
        }
      );
    } catch {
      toast.error('Toggle Failed', {
        description: 'Unable to update agent status. Please try again.',
      });
    }
  }, [currentIsActive, toggleDefaultAgentStatus]);

  return (
    <div className='space-y-6'>
      {/* Current Default Agent Display - Matches Ticket Detail Page Styling */}
      <div className='p-4 bg-muted/50 rounded-lg border border-muted'>
        <div className='flex items-center gap-3'>
          {displayAgent ? (
            <>
              <ProfileAvatar
                avatarUrl={displayAgent.avatar_url || null}
                name={displayAgent.name}
                email={displayAgent.email}
                clerkId={displayAgent.clerk_id || ''}
                className={cn(
                  'h-10 w-10',
                  !currentIsActive && 'grayscale opacity-75'
                )}
                fallbackClassName='bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 text-xs font-medium'
              />
              <div className='flex-1'>
                <h4 className='font-medium text-gray-900 dark:text-gray-100 text-sm'>
                  {displayAgent.name}
                </h4>
                <p className='text-xs text-gray-500 dark:text-gray-400'>
                  {displayAgent.email}
                </p>
              </div>
              <div className='flex items-center gap-2'>
                <Switch
                  checked={currentIsActive}
                  onCheckedChange={handleToggleActive}
                  disabled={isLoading}
                />
                <span
                  className={cn(
                    'text-xs font-medium',
                    currentIsActive
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  )}
                >
                  {currentIsActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </>
          ) : (
            <>
              <div className='h-10 w-10 rounded-full bg-muted flex items-center justify-center'>
                <User className='w-5 h-5 text-muted-foreground' />
              </div>
              <div className='flex-1'>
                <h4 className='font-medium text-gray-900 dark:text-gray-100 text-sm'>
                  No default agent set
                </h4>
                <p className='text-xs text-gray-500 dark:text-gray-400'>
                  Auto-assignment disabled
                </p>
              </div>
              <div className='flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-yellow-500' />
                <span className='text-xs text-yellow-600 dark:text-yellow-400 font-medium'>
                  Not Set
                </span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Agent Selection */}
      <div className='space-y-3'>
        <Label htmlFor='default-agent' className='text-sm font-medium'>
          Select Default Agent
        </Label>
        <div className='space-y-2'>
          {displayAgent ? (
            <div className='flex h-10 w-full min-w-0 rounded-md border border-input bg-muted/50 px-3 py-1 text-sm transition-colors items-center justify-between cursor-not-allowed'>
              <span className='text-muted-foreground'>
                {displayAgent.email}
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveSelected();
                }}
                disabled={isLoading}
                className='h-6 w-6 cursor-pointer rounded-full text-muted-foreground hover:text-foreground disabled:opacity-50 flex items-center justify-center text-lg font-medium transition-colors'
                title='Remove agent'
              >
                ×
              </button>
            </div>
          ) : (
            <UserAutocomplete
              value=''
              onChange={handleAgentChange}
              placeholder='Search for an agent or admin...'
              roleFilter={['admin', 'agent']}
              multiple={false}
              dropdownOnly={true}
              returnUserIds={true}
              className='h-10'
              disabled={isLoading}
            />
          )}
          <p className='text-xs text-muted-foreground'>
            This agent will be automatically assigned to new tickets when no
            department-specific rules apply.
          </p>
        </div>
      </div>

      {/* Information Box */}
      <div className='bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4'>
        <div className='flex items-start gap-3'>
          <AlertCircle className='w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0' />
          <div className='space-y-2'>
            <h4 className='text-sm font-medium text-blue-900 dark:text-blue-100'>
              How Default Agent Assignment Works
            </h4>
            <ul className='text-xs text-blue-800 dark:text-blue-200 space-y-1'>
              <li>
                • New tickets are first checked against department-specific
                assignment rules
              </li>
              <li>
                • If no department rule matches, the default agent is assigned
              </li>
              <li>• If no default agent is set, tickets remain unassigned</li>
              <li>
                • Only users with Admin or Agent roles can be selected as
                default agents
              </li>
              <li>
                • This setting applies to all new tickets created in your
                organization
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export const DefaultAgentSelector = memo(DefaultAgentSelectorComponent);
