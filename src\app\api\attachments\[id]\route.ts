import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const serviceSupabase = createServiceSupabaseClient();

    // Get attachment metadata
    const { data: attachment, error: attachmentError } = await serviceSupabase
      .from('attachments')
      .select('*')
      .eq('id', id)
      .single();

    if (attachmentError || !attachment) {
      return NextResponse.json(
        { error: 'Attachment not found' },
        { status: 404 }
      );
    }

    // Verify user has access to this attachment's tenant
    const { data: user, error: userError } = await serviceSupabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .eq('tenant_id', attachment.tenant_id)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get the file from Supabase storage
    const { data: fileData, error: downloadError } =
      await serviceSupabase.storage
        .from(attachment.storage_bucket)
        .download(attachment.storage_path);

    if (downloadError || !fileData) {
      return NextResponse.json(
        { error: 'Failed to download file' },
        { status: 500 }
      );
    }

    // Convert blob to array buffer
    const arrayBuffer = await fileData.arrayBuffer();

    // Return the file with appropriate headers
    return new NextResponse(arrayBuffer, {
      status: 200,
      headers: {
        'Content-Type': attachment.file_type,
        'Content-Disposition': `attachment; filename="${attachment.file_name}"`,
        'Content-Length': attachment.file_size.toString(),
      },
    });
  } catch (error) {
    console.error('Attachment download error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
