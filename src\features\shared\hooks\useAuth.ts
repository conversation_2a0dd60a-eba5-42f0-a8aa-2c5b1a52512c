'use client';

import { useUser, useClerk, useOrganization } from '@clerk/nextjs';
import { getDomainFromWindow } from '@/lib/domain';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo, useRef } from 'react';
import { useTicketingUIActions } from '@/features/ticketing/store/use-ticketing-store';
import { useQueryClient } from '@tanstack/react-query';
import { clearAllCaches } from '@/providers/ReactQueryProvider';
import { resetGlobalSupabaseClient } from '@/lib/supabase-clerk';

export interface AuthState {
  user: ReturnType<typeof useUser>['user'];
  isLoaded: boolean;
  isSignedIn: boolean;
  isTokenReady: boolean;
  tenantId: string | null;
  role: string;
  isSuperAdmin: boolean;
  isAdmin: boolean;
  isAgent: boolean;
  isUser: boolean;
  isLoggingOut: boolean;
}

export interface AuthActions {
  signOut: () => Promise<void>;
  navigate: (path: '/tickets' | '/sign-in' | '/sign-up') => void;
}

/**
 * Optimized authentication hook with modern 2025 patterns
 */
export function useAuth(): AuthState & AuthActions;
export function useAuth<T>(selector: (state: AuthState & AuthActions) => T): T;
export function useAuth<T>(
  selector?: (state: AuthState & AuthActions) => T
): T | (AuthState & AuthActions) {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut: clerkSignOut } = useClerk();
  const { membership } = useOrganization();

  // Get tenant ID directly from domain to avoid Zustand store issues
  const tenantId = useMemo(() => {
    if (typeof window === 'undefined') return null;
    const domainInfo = getDomainFromWindow(window);
    return domainInfo.tenantId;
  }, []);

  const router = useRouter();
  const queryClient = useQueryClient();

  // AbortController for canceling requests during logout
  const logoutAbortControllerRef = useRef<AbortController | null>(null);

  // Get store actions for clearing state on logout
  const resetUIState = useTicketingUIActions.useResetUIState();

  const role = useMemo(() => {
    if (!isSignedIn || !membership) {
      return 'user';
    }

    const clerkRole = membership.role;

    switch (clerkRole) {
      case 'org:super_admin':
        return 'super_admin';
      case 'org:admin':
        return 'admin';
      case 'org:agent':
        return 'agent';
      case 'org:member':
        return 'user';
      default:
        if (
          user?.emailAddresses?.[0]?.emailAddress === '<EMAIL>'
        ) {
          return 'super_admin';
        }
        return 'user';
    }
  }, [isSignedIn, membership, user?.emailAddresses]);

  const isSuperAdmin = role === 'super_admin';
  const isAdmin = role === 'admin' || isSuperAdmin;
  const isAgent = role === 'agent';
  const isUser = role === 'user';

  // SIMPLIFIED FIX: Use isLoaded && isSignedIn as token ready indicator
  // This avoids the useEffect issue and provides a simpler solution
  const isTokenReady = isLoaded && isSignedIn;

  const signOut = useCallback(async () => {
    try {
      // Modern React 19 pattern: Optimized logout sequence
      logoutAbortControllerRef.current = new AbortController();

      // CRITICAL FIX: Set global logout flag to prevent token access race conditions
      (globalThis as Record<string, unknown>)._clerkLogoutInProgress = true;

      // Clear local state first to prevent new API calls
      resetUIState();

      // CRITICAL FIX: Cancel all pending queries before clearing cache to prevent race conditions
      await queryClient.cancelQueries();

      // CRITICAL FIX: Reset global Supabase client to prevent stale session references
      resetGlobalSupabaseClient();

      // Clear React Query cache - modern 2025 pattern
      queryClient.clear();

      // Clear all persistent caches (React Query + Dexie)
      await clearAllCaches();

      await clerkSignOut();

      // Instant navigation without loading states
      router.push('/sign-in');
    } catch (error) {
      console.error('Error during logout:', error);
      window.location.href = '/sign-in';
    } finally {
      // Clear global logout flag
      (globalThis as Record<string, unknown>)._clerkLogoutInProgress = false;
      logoutAbortControllerRef.current = null;
    }
  }, [clerkSignOut, router, resetUIState, queryClient]);

  const navigate = useCallback(
    (path: '/tickets' | '/sign-in' | '/sign-up') => {
      router.push(path);
    },
    [router]
  );

  // Check if logout is in progress
  const isLoggingOut = logoutAbortControllerRef.current !== null;

  const state = {
    user,
    isLoaded,
    isSignedIn: isSignedIn || false,
    isTokenReady,
    tenantId,
    role,
    isSuperAdmin,
    isAdmin,
    isAgent,
    isUser,
    isLoggingOut,
    signOut,
    navigate,
  };

  return selector ? selector(state) : state;
}

/**
 * Hook for checking if user has specific permissions
 */
export function usePermissions() {
  const { isSuperAdmin, isAdmin, isAgent, isUser } = useAuth();

  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (isSuperAdmin || isAdmin) return true;

      // Tenant features now handled by React Query - simplified for now

      switch (permission) {
        case 'tickets.view':
          return isUser || isAgent;
        case 'tickets.create':
          return isSuperAdmin || isAdmin || isUser;
        case 'tickets.update':
          return isAgent || isAdmin || isSuperAdmin;
        case 'tickets.delete':
          return isAdmin || isSuperAdmin;
        case 'tickets.assign':
          return isAdmin || isSuperAdmin;
        case 'tickets.priority.change':
          return isAdmin || isSuperAdmin;
        case 'tickets.department.change':
          return isAdmin || isSuperAdmin;
        case 'analytics.view':
          return isAgent || isAdmin || isSuperAdmin; // Simplified - tenant features via React Query
        case 'integrations.manage':
          return isAdmin || isSuperAdmin; // Simplified - tenant features via React Query
        case 'users.manage':
          return isAdmin || isSuperAdmin;
        case 'settings.manage':
          return isAdmin || isSuperAdmin;
        default:
          return false;
      }
    },
    [isSuperAdmin, isAdmin, isAgent, isUser]
  );

  const canAccessFeature = (): boolean => {
    // Simplified - tenant features now handled by React Query
    return isAdmin || isSuperAdmin;
  };

  return {
    hasPermission,
    canAccessFeature,
    isSuperAdmin,
    isAdmin,
    isAgent,
    isUser,
  };
}
