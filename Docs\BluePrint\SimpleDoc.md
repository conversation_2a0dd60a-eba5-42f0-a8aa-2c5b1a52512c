# SimpleDoc — Part 1: System Architecture & Project Setup with Tenant Isolation (Updated for React Query v5)

---

## 1. Architecture Overview

- The app is a **modular monolith** with clear domain modules like `auth`, `ticketing`, `notifications`, and `teams`.
- Built for **multi-tenancy**, allowing multiple organizations to securely share the same system.
- Data isolation is achieved using a shared database schema, partitioned logically by a `tenant_id` field.
- Tenant isolation is enforced across the frontend, backend APIs, and database layers.
- **React Query v5.83.0** manages server-state with smart caching, persistence, background refetching, and offline support.
- Zustand manages UI-local state and integrates seamlessly with React Query caches.

---

## 2. Multi-Tenancy & Tenant Isolation

- Each tenant represents a distinct organization or client.
- Supabase Row Level Security (RLS) ensures tenants can only access their own data, enforced using JWT claims.
- Clerk provides JWT tokens embedding `tenant_id` and `role` claims for secure backend and React Query fetchers.
- React Query queries and mutations use `tenant_id` in query keys for scoped caching per tenant.

---

## 3. Technology Stack (Updated)

| Technology                     | Purpose and Tenant Awareness                                       |
| ------------------------------ | ------------------------------------------------------------------ |
| **Next.js 15.4.1**             | Frontend/backend framework with tenant-scoped APIs                 |
| **React 19.1.0**               | Component rendering and UI updates for tenant-specific data        |
| **React Query v5.83.0**        | Advanced data fetching, caching, persistence, and DevTools support |
| **Clerk 6.25.0**               | Authentication and tenant-aware JWT tokens                         |
| **Supabase JS 2.51.0**         | Database SDK with RLS and realtime updates per tenant              |
| **Zustand 5.0.6**              | Frontend state management scoped to active tenant                  |
| **Dexie.js 4.0.11**            | Offline caching and persistence layer for React Query              |
| **Zod 4.0.5**                  | Tenant-aware validation for API inputs                             |
| **Tailwind CSS 4.x** + Plugins | Utility-first styling framework                                    |
| **Radix UI v1.x**              | UI primitives for dialogs, menus, and other interactions           |
| **Lucide React 0.525.0**       | Clean, scalable icon library                                       |
| **Sonner 2.0.6**               | Toast notifications and system alerts                              |

---

## 4. Data Flow with Tenant Awareness

- Clerk’s JWT tokens containing tenant and role claims are sent with every API request.
- Backend APIs validate tenant claims and enforce scoped queries.
- React Query manages server-state cache scoped per tenant, with background refetches for data freshness.
- Zustand maintains UI state like filters, modals, and active tabs.
- Supabase realtime channels push live updates scoped to each tenant.

---

## 5. Hybrid Client Sync Strategy

- **Local First:** React Query loads tenant-specific data from Dexie.js IndexedDB for instant UI responsiveness.
- **Realtime Updates:** Supabase channels notify React Query caches of data changes for live collaboration.
- **Server Fallback:** React Query fetches missing or stale data in the background without blocking UI.
- **Optimistic UI:** Local changes are reflected immediately in Zustand and React Query caches before syncing with the backend.

---

## 6. Benefits of This Setup

- ⚡ **Fast UI** with React Query caching and Dexie.js persistence.
- 📶 **Offline-first support** for seamless user experience.
- 🔐 **Strict Tenant Isolation** enforced across all layers.
- 🔄 **Scalable and Maintainable** architecture designed for future AI integrations and workflows.

# SimpleDoc — Part 2: Authentication & Tenant-Aware User Management (Updated for React Query v5)

---

## 1. Authentication with Clerk and React Query

- **Clerk 6.25.0** handles user authentication flows including sign-up, sign-in, password resets, and session management.
- Clerk issues JWT tokens embedding `user_id`, `tenant_id`, and `role` claims to establish user identity and tenant context.
- React Query integrates with Clerk sessions by providing tenant-aware query context for secure data fetching.

---

## 2. User-Tenant Association & Syncing

- On login or signup, backend synchronizes Clerk user data with the Supabase `users` table, linking users to their `tenant_id` and roles.
- React Query queries the `users` table using tenant-scoped query keys for cache isolation.
- Changes to user records (e.g., role updates) are propagated to React Query caches and Zustand session store for real-time frontend updates.

| Field                      | Description                             |
| -------------------------- | --------------------------------------- |
| `id`                       | UUID primary key                        |
| `tenant_id`                | UUID foreign key to tenant              |
| `clerk_id`                 | Clerk user identifier                   |
| `email`                    | User email                              |
| `role`                     | Tenant-scoped role (user, agent, admin) |
| `created_at`, `updated_at` | Timestamps                              |

---

## 3. Tenant-Aware JWT Tokens & Claims

- JWT tokens issued by Clerk include claims for secure, tenant-aware access:

```json
{
  "sub": "user-uuid",
  "tenant_id": "tenant-uuid",
  "role": "agent",
  "iat": 1723547800,
  "exp": 1723551400
}
```

- React Query uses these claims in fetchers and query contexts to scope API requests and caching.
- Supabase RLS policies enforce backend data isolation based on `tenant_id` from JWT.

---

## 4. Role-Based Access Control (RBAC)

- Define tenant-scoped roles to control user permissions:

| Role            | Permissions                                          |
| --------------- | ---------------------------------------------------- |
| **User**        | Create tickets, view/respond to own tickets only     |
| **Agent**       | Manage tickets assigned within their tenant/team     |
| **Admin**       | Manage users, teams, and tickets within their tenant |
| **Super Admin** | (Optional) Full system-wide access across tenants    |

- React Query enforces role-specific access by scoping queries and mutations.

---

## 5. Frontend Session and State Management

- Clerk’s React hooks (`useUser()`) provide authenticated user metadata including tenant and role info.
- Zustand manages frontend session state, storing `tenant_id` and `role` for reactive UI rendering.
- React Query queries and mutations consume tenant context to enforce cache scoping.
- Cache is securely cleared on logout or tenant switch.

---

## 6. React Query v5 Integration for Authentication

- Integrate Clerk session context into React Query’s provider for consistent access in hooks.
- React Query’s `onError` handlers manage authentication errors such as expired tokens.
- Dexie.js persistence maintains authentication context during offline usage and reconnects.

---

## 7. Security Considerations

- Verify JWT signatures and expiration in backend APIs.
- Reject requests where `tenant_id` in the JWT does not match the resource tenant.
- Use HTTPS and secure cookie/token storage on frontend.
- Configure React Query to refresh tokens and cache in the background for seamless UX.

# SimpleDoc — Part 3: Tenant-Aware Data Modeling & State Management (Updated for React Query v5)

---

## 1. Database Schema with Tenant Isolation

- All tenant-specific tables include a mandatory **`tenant_id` UUID** field to enforce strict tenant data isolation.
- Supabase Row Level Security (RLS) policies enforce backend data isolation based on `tenant_id`.
- React Query queries and mutations include tenant context to scope API calls and caching.

### Key Tables and Tenant Columns

| Table        | Tenant Column | Purpose                      |
| ------------ | ------------- | ---------------------------- |
| `users`      | `tenant_id`   | Associates users to tenants  |
| `tickets`    | `tenant_id`   | Tickets owned by tenants     |
| `responses`  | `tenant_id`   | Ticket conversation messages |
| `audit_logs` | `tenant_id`   | Tenant-scoped audit trail    |

---

## 2. Input Validation Using Zod

- Use **Zod 4.0.5** schemas for validating API inputs and ensuring `tenant_id` is present.
- React Query integrates with Zod to validate API responses and mutations.

```ts
import { z } from 'zod';

export const createTicketSchema = z.object({
  tenant_id: z.string().uuid(),
  user_id: z.string().uuid(),
  subject: z.string().min(5),
  description: z.string().min(10),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  category: z.string().optional(),
});
```

---

## 3. Frontend State Management with Zustand and React Query

- Zustand manages UI-local state (filters, modals, tabs).
- **React Query v5.83.0** handles server-state (queries and mutations) with tenant-aware query keys.
- Tenant-aware hooks combine Zustand for UI reactivity and React Query for cache updates.

```ts
const useStore = create((set) => ({
  tenant_id: null,
  tickets: [],
  setTenant: (tenantId) => set({ tenant_id: tenantId }),
  setTickets: (tickets) => set({ tickets }),
}));
```

---

## 4. Dexie.js Persistent Cache with React Query

- Dexie.js 4.0.11 integrates with React Query for offline caching using `createPersister`.
- Cached data loads instantly from IndexedDB and syncs in the background.
- React Query clears caches on logout or tenant switch to avoid cross-tenant data leaks.

---

## 5. Real-Time Updates with Supabase

- Supabase realtime channels push tenant-filtered data changes.
- React Query hooks update caches automatically, syncing Zustand slices and Dexie.js.

---

## 6. Soft Delete Strategy

- Tickets are soft deleted using a status flag (`closed`).
- React Query queries exclude closed tickets from active views but keep them for audit purposes.

# SimpleDoc — Part 4: Ticket Lifecycle & Status Management with Tenant Isolation (Updated for React Query v5)

---

## 1. Ticket Status Definitions

Tickets follow a well-defined lifecycle, scoped within each tenant context:

| Status        | Description                                             |
| ------------- | ------------------------------------------------------- |
| **New**       | Created and awaiting assignment within the tenant       |
| **Open**      | Actively being worked on by tenant support agents       |
| **Pending**   | Waiting on tenant user input or additional information  |
| **Resolved**  | Issue believed fixed, awaiting tenant user confirmation |
| **Closed**    | Finalized and archived no further action                |
| **Escalated** | Requires urgent attention due to SLA breach or priority |

---

## 2. Status Transitions & Business Rules

- Transitions are tenant-scoped and validated using tenant context from JWT tokens.
- React Query handles server-state mutations for status changes, applying optimistic updates to the UI.
- Typical transitions include:
  - `New → Open`: Agent accepts ticket.
  - `Open → Pending`: Waiting on user response.
  - `Pending → Open`: User replies.
  - `Open → Resolved`: Agent marks as resolved.
  - `Resolved → Closed`: User confirms or timeout auto-closes ticket.
  - Any → `Escalated`: Triggered byq SLA breaches or critical issues.

Each transition triggers:

- React Query cache updates for tenant-specific queries.
- Supabase realtime events to synchronize status across all active clients.
- Audit logs to track lifecycle changes.

---

## 3. Soft Delete via Closed Status

- Soft delete is implemented by setting status to `Closed`.
- Closed tickets are excluded from active React Query queries but retained in the database and IndexedDB for audit purposes.

---

## 4. Tenant-Aware Audit Logging

Each status change and escalation is recorded with tenant context:

| Field         | Description                         |
| ------------- | ----------------------------------- |
| `tenant_id`   | Tenant owning the ticket            |
| `ticket_id`   | Ticket being modified               |
| `user_id`     | User performing the action          |
| `action_type` | e.g., `status_change`, `escalation` |
| `old_value`   | Previous status                     |
| `new_value`   | New status                          |
| `timestamp`   | Time of change                      |

---

## 5. SLA Enforcement & Escalations

- Backend jobs (e.g., Supabase Edge Functions) monitor SLA deadlines per tenant.
- React Query’s `refetchInterval` keeps escalated statuses synced in UI.
- Notifications are sent to relevant tenant agents and admins.

---

## 6. React Query v5 Integration

- React Query `useMutation` hooks handle status updates with optimistic UI.
- Supabase realtime updates push changes instantly to React Query caches.
- Cache invalidation ensures that all active tenants see accurate ticket statuses.

# SimpleDoc — Part 5: Priority, Deadlines & Escalations with Tenant Isolation (Updated for React Query v5)

---

## 1. Priority Levels

Each ticket has a priority level scoped to its tenant, indicating urgency and influencing SLA enforcement:

| Priority   | Description                                     |
| ---------- | ----------------------------------------------- |
| **Low**    | Minor issues; longer resolution time acceptable |
| **Medium** | Standard priority with typical SLA timelines    |
| **High**   | Requires quicker response and handling          |
| **Urgent** | Critical issues needing immediate attention     |

---

## 2. Setting Priorities

- Users select priority when creating tickets.
- Tenant agents and admins can override priority to reflect actual urgency.
- A default priority (typically Medium) is assigned if none is specified.
- React Query uses priority-aware query keys and caching strategies to ensure UI reflects priority changes.

---

## 3. Deadlines and Service Level Agreements (SLAs)

- Each priority level corresponds to a tenant-specific SLA deadline:

| Priority | SLA Deadline |
| -------- | ------------ |
| Urgent   | 4 hours      |
| High     | 1 day        |
| Medium   | 3 days       |
| Low      | 7 days       |

- Deadlines are set on ticket creation or when priority changes.
- Supabase Edge Functions monitor SLA deadlines and trigger escalations.

---

## 4. Automatic Priority Escalation

- Backend jobs periodically check tickets against SLA deadlines.
- Overdue tickets automatically escalate to a higher priority within their tenant scope.
- React Query’s `refetchInterval` keeps escalations synced in the UI.
- Notifications are sent to tenant support teams and all escalations are recorded in audit logs.

---

## 5. Manual Priority Management

- Tenant agents and admins can manually update ticket priorities via UI or API.
- React Query mutations apply changes optimistically and update caches before syncing with the backend.
- Supabase realtime subscriptions propagate updates across all active clients.

---

## 6. React Query v5 Integration for Priority Management

- React Query hooks (`useMutation`, `useQuery`) handle priority updates and SLA deadline tracking.
- Dexie.js persistence ensures priority changes are cached offline and synchronized when online.
- Supabase realtime channels update all active React Query caches with priority and escalation changes.

# SimpleDoc — Part 6: Roles, Teams, Auditing & Monitoring with Tenant Isolation (Updated for React Query v5)

---

## 1. User Roles and Permissions

- Define tenant-scoped roles to control user capabilities within their tenant context:

| Role              | Permissions                                                    |
| ----------------- | -------------------------------------------------------------- |
| **User**          | Create tickets, view/respond to own tickets only               |
| **Support Agent** | Manage tickets assigned to their tenant/team                   |
| **Admin**         | Manage users, teams, tickets, and settings within their tenant |
| **Super Admin**   | (Optional) Full system-wide access across tenants              |

- Roles are stored in the `users` table and linked to `tenant_id`.
- React Query queries and mutations are scoped using role-aware query keys to prevent unauthorized data access.
- Supabase RLS enforces data access control based on roles.

---

## 2. Team Management

- Agents are grouped into tenant-scoped teams (e.g., Support, Sales).
- Tickets can be assigned to individual users or teams within the tenant.
- Tenant admins can assign or reassign tickets using React Query mutations, ensuring immediate UI updates.

---

## 3. Audit Logging

- Audit logs track critical actions such as:
  - Ticket status and priority changes
  - User and team management events
  - Ticket assignments

| Field         | Description                         |
| ------------- | ----------------------------------- |
| `tenant_id`   | Tenant owning the action            |
| `ticket_id`   | Ticket affected (if applicable)     |
| `user_id`     | User performing the action          |
| `action_type` | e.g., `status_change`, `assignment` |
| `old_value`   | Previous value                      |
| `new_value`   | New value                           |
| `timestamp`   | Time of change                      |

- React Query’s `onSuccess` hooks update cached audit logs after mutations.
- Supabase realtime subscriptions push audit log changes to all connected clients.

---

## 4. Monitoring and Dashboards

- Dashboards for tenant admins and super admins display:
  - Ticket volumes and status distributions
  - SLA compliance and escalation metrics
  - Audit log summaries scoped per tenant

- React Query queries fetch dashboard data with caching and background refetching for near real-time updates.

---

## 5. Approval Workflows (Optional)

- Approval workflows for sensitive actions (e.g., priority escalations):
  - Agents submit change requests.
  - Tenant admins approve or reject requests.
  - React Query mutations ensure state changes are propagated across all active clients.

---

## 6. React Query v5 Integration for Roles and Monitoring

- Role-based API endpoints are consumed using React Query `queryOptions` for access control.
- Supabase realtime channels ensure role and team updates are reflected in all connected clients.
- React Query DevTools assist developers in monitoring tenant-scoped queries and cache state.

# SimpleDoc — Part 7: Final Wrap-up and Best Practices (Updated for React Query v5)

---

## 1. Summary of Tenant-Aware Architecture

- The system is a **modular monolith** that supports secure multi-tenancy.
- Tenant isolation is enforced across all layers: frontend (React 19), backend (Next.js 15.4.1), database (Supabase RLS), and authentication (Clerk 6.25.0).
- **React Query v5** manages server-state with advanced caching, background synchronization, and offline persistence.
- Zustand complements React Query by managing UI-local state and session metadata.
- Dexie.js provides persistent caching for offline-first capability.
- RBAC and team management enforce permissions securely.
- Audit logs and dashboards ensure tenant-level accountability and visibility.

---

## 2. Best Practices for React Query v5 Integration

### Tenant Isolation

- Include `tenant_id` in every React Query query key to scope caching per tenant.
- Enforce tenant filtering with Supabase RLS policies and validate JWT tokens for all API calls.

### React Query Patterns

- Use **QueryOptions Factory** for type-safe, consistent API configurations across components.
- Prefer `useQuery`, `useMutation`, and `useInfiniteQuery` for declarative data fetching and pagination.
- Configure `staleTime` and `gcTime` for a balance between data freshness and performance.
- Enable React Query DevTools for cache inspection and background sync monitoring.

### State Management

- Use React Query for server state and Zustand for UI-local state.
- Keep Zustand slices and React Query caches synchronized on tenant switches.

### Offline Support

- Integrate Dexie.js with React Query’s `createPersister` for offline persistence.
- Preload tenant-scoped data from IndexedDB for instant UI rendering and background synchronization.

### Security Considerations

- Validate JWT tokens and role claims in all backend APIs.
- React Query `onError` handlers handle token expiration and auto-refresh logic.
- Securely clear caches on logout or tenant changes.

---

## 3. Operational Recommendations

- Monitor tenant usage and SLA compliance through dashboards.
- Implement alerts for system health and tenant-specific metrics.
- Automate RBAC and tenant scoping tests for reliable data isolation.
- Plan for future scaling by modularizing domain logic.

---

## 4. Future Enhancements

- Add tenant-specific feature flags and configuration options.
- Extend approval workflows for high-privilege operations.
- Introduce AI-driven automation and proactive notifications.
- Expand audit logging for fine-grained user actions.

---

## 5. Developer Collaboration Guidelines

- Apply SOLID, DRY, KISS, and YAGNI principles for clean and maintainable code.
- Standardize React Query usage patterns across teams.
- Automate tenant-aware integration tests to maintain secure multi-tenancy.
