'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

interface CheckboxProps
  extends Omit<React.ComponentProps<'input'>, 'type' | 'onChange'> {
  label?: string;
  description?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, description, id, ...props }, ref) => {
    const generatedId = React.useId();
    const checkboxId = id || generatedId;

    return (
      <div className='flex items-start gap-3 mb-4'>
        <div className='relative flex items-center mt-[2px]'>
          <input
            type='checkbox'
            id={checkboxId}
            ref={ref}
            className={cn(
              'peer h-4 w-4 shrink-0 rounded border border-gray-300 bg-white shadow-sm transition-all',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
              'checked:bg-blue-600 checked:border-blue-600',
              'disabled:cursor-not-allowed disabled:opacity-50',
              'dark:border-gray-600 dark:bg-gray-800 dark:checked:bg-blue-600 dark:checked:border-blue-600',
              className
            )}
            {...props}
          />
          <Check className='absolute left-0.5 top-0.5 h-3 w-3 text-white opacity-0 transition-opacity peer-checked:opacity-100 pointer-events-none' />
        </div>
        {(label || description) && (
          <div className='flex flex-col gap-1'>
            {label && (
              <label
                htmlFor={checkboxId}
                className='text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer select-none'
              >
                {label}
              </label>
            )}
            {description && (
              <p className='text-xs text-gray-500 dark:text-gray-400'>
                {description}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
