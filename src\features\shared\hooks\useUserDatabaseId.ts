import { useQuery } from '@tanstack/react-query';
import { useUser } from '@clerk/nextjs';
import { useClerkSupabaseSync } from '@/hooks/useClerkSupabaseSync';
import { getDomainFromWindow } from '@/lib/domain';

/**
 * Optimized React Query hook to resolve user's database UUID from Clerk ID
 * Prevents duplicate API calls and provides proper caching
 * Waits for sync completion to prevent race conditions
 */
export function useUserDatabaseId() {
  const { user, isLoaded } = useUser();

  // Get tenant ID and sync status to prevent race conditions
  const tenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;
  const syncStatus = useClerkSupabaseSync(tenantId);

  const query = useQuery({
    queryKey: ['user-database-id', user?.id],
    queryFn: async () => {
      // Additional safety: Wait a bit more to ensure sync is truly complete
      if (syncStatus.isComplete && !syncStatus.tenantExists) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      const response = await fetch('/api/user/database-id', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user database ID: ${response.status}`);
      }

      const { databaseId } = await response.json();
      return databaseId;
    },
    enabled:
      isLoaded &&
      !!user?.id &&
      syncStatus.isComplete &&
      !syncStatus.isLoading &&
      syncStatus.tenantExists && // Ensure tenant actually exists
      syncStatus.userExists, // Ensure user actually exists
    staleTime: 5 * 60 * 1000, // 5 minutes - user database ID rarely changes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    userDatabaseId: query.data || '',
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    clerkId: user?.id || '',
  };
}
