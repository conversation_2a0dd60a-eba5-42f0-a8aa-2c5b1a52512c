import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { TenantDepartment } from './useDepartments';
import { toast } from '@/features/shared/components/toast';

interface CreateDepartmentData {
  name: string;
  color: string;
  dot_color: string;
  icon?: string;
  is_active?: boolean;
}

/**
 * Hook for creating a new department
 */
export function useCreateDepartment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateDepartmentData
    ): Promise<TenantDepartment> => {
      const response = await fetch('/api/departments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create department');
      }

      return response.json();
    },
    onMutate: async (newDepartment) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ['departments'] });

      // Snapshot the previous value
      const previousDepartments = queryClient.getQueryData(['departments']);

      // Optimistically update to the new value
      queryClient.setQueryData(
        ['departments'],
        (old: TenantDepartment[] | undefined) => {
          if (!old) return old;

          // Create optimistic department with temporary ID
          const optimisticDepartment: TenantDepartment = {
            id: `temp-${Date.now()}`, // Temporary ID
            name: newDepartment.name,
            color: newDepartment.color,
            dot_color: newDepartment.dot_color,
            icon: newDepartment.icon || 'folder',
            is_active: newDepartment.is_active ?? true,
            tenant_id: '', // Will be set by server
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          return [...old, optimisticDepartment];
        }
      );

      // Show optimistic success toast
      toast.success('Department Created', {
        description: `"${newDepartment.name}" has been added to your organization.`,
      });

      // Return a context object with the snapshotted value
      return { previousDepartments };
    },
    onError: (error: Error, _newDepartment, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(['departments'], context?.previousDepartments);

      toast.error('Failed to Create Department', {
        description: error.message,
      });
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: ['departments'] });
    },
  });
}

/**
 * Hook for toggling department active status
 */
export function useToggleDepartmentStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      id: string;
      is_active: boolean;
    }): Promise<TenantDepartment> => {
      const response = await fetch('/api/departments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to toggle department status');
      }

      return response.json();
    },
    onSuccess: (updatedDepartment) => {
      // Invalidate and refetch department queries
      queryClient.invalidateQueries({ queryKey: ['departments'] });

      // Show success toast only after server confirmation
      toast.success(
        updatedDepartment.is_active
          ? 'Department Activated'
          : 'Department Deactivated',
        {
          description: updatedDepartment.is_active
            ? `"${updatedDepartment.name}" is now available for new tickets.`
            : `"${updatedDepartment.name}" is hidden from new ticket creation.`,
        }
      );
    },
    onError: (error: Error) => {
      toast.error('Failed to Update Department Status', {
        description: error.message,
      });
    },
  });
}

/**
 * Hook for deleting a department with optimized error handling
 */
export function useDeleteDepartment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (departmentId: string): Promise<void> => {
      try {
        const response = await fetch(`/api/departments?id=${departmentId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          const errorMessage =
            errorData.details ||
            errorData.error ||
            'Failed to delete department';

          // Create a clean error without exposing internal details
          const error = new Error(errorMessage);
          (error as Error & { errorData?: unknown }).errorData = errorData;
          throw error;
        }
      } catch (error) {
        // Ensure we don't leak internal errors to the UI
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Failed to delete department');
      }
    },
    onSuccess: () => {
      // Invalidate and refetch department queries
      queryClient.invalidateQueries({ queryKey: ['departments'] });

      toast.success('Department Deleted', {
        description: 'The department has been permanently removed.',
      });
    },
    onError: (
      error: Error & { errorData?: { error?: string; details?: string } }
    ) => {
      // Clean error handling to prevent Next.js error overlay
      const errorData = error.errorData;
      const errorTitle = errorData?.error || 'Cannot Delete Department';
      const errorDescription = error.message;

      // Log error for debugging but don't expose to user
      console.warn('Department deletion failed:', error);

      toast.error(errorTitle, {
        description: errorDescription,
      });
    },
  });
}
