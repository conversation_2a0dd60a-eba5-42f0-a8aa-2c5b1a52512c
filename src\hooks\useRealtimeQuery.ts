import { useEffect, useMemo } from 'react';
import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import type { Database } from '@/types/supabase';
import RealtimeDataService from '@/lib/services/realtime-data.service';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import { useUnifiedRealtimeSubscription } from './useUnifiedRealtimeSubscription';
import { useAuth as useClerkAuth } from '@clerk/nextjs';

// Type for database row
type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];

// Simple rate limiting for missing ticket fetches
const missingTicketFetches = new Map<string, number>();

function canFetchMissingTicket(ticketId: string): boolean {
  const now = Date.now();
  const lastFetch = missingTicketFetches.get(ticketId) || 0;

  // Allow fetch if more than 6 seconds have passed (10 fetches per minute max)
  return now - lastFetch > 6000;
}

function trackMissingTicketFetch(ticketId: string): void {
  missingTicketFetches.set(ticketId, Date.now());

  // Clean up old entries every 100 fetches
  if (missingTicketFetches.size > 100) {
    const cutoff = Date.now() - 60000; // 1 minute ago
    for (const [id, timestamp] of missingTicketFetches.entries()) {
      if (timestamp < cutoff) {
        missingTicketFetches.delete(id);
      }
    }
  }
}

/**
 * ✅ Recommended Pattern: useRealtimeQuery() Custom Hook (Modern Supabase + React Query)
 *
 * Based on latest 2025 patterns from Docs/LatestPatterns.md
 *
 * This hook encapsulates:
 * - Fetching data with React Query
 * - Subscribing to Supabase real-time changes
 * - Automatically updating the cache when DB changes occur
 *
 * Benefits:
 * - Minimal, modern, robust
 * - Clean separation of concerns
 * - No manual subscription management
 * - Automatic cache updates on real-time events
 */
export function useRealtimeQuery<T extends { id: string }>(
  queryKey: string[],
  fetchFn: () => Promise<T[]>,
  table: string,
  options?: {
    filter?: string;
    schema?: string;
    queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'>;
  }
) {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // Initialize RealtimeDataService for proper user data transformation
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase),
    [supabase]
  );

  // Memoize queryKey to prevent subscription churn
  const stableQueryKey = useMemo(() => queryKey, [queryKey]);

  // Memoize options to prevent subscription churn
  const stableOptions = useMemo(() => options, [options]);

  // React Query for data fetching
  const query = useQuery({
    queryKey: stableQueryKey,
    queryFn: fetchFn,
    ...stableOptions?.queryOptions,
  });

  // ✅ Simple real-time subscription following recommended pattern
  useEffect(() => {
    if (!user) return;

    const channel = supabase
      .channel(`realtime:${table}`)
      .on(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        'postgres_changes' as any,
        {
          event: '*',
          schema: stableOptions?.schema || 'public',
          table,
          filter: stableOptions?.filter,
        },
        async (
          payload: RealtimePostgresChangesPayload<Record<string, unknown>>
        ) => {
          const { eventType, new: newRow, old: oldRow } = payload;

          // CRITICAL FIX: Only skip INSERT events for messages to prevent duplicate optimistic updates
          // Allow all UPDATE events (including status changes) to ensure proper synchronization
          if (
            userDatabaseId &&
            eventType === 'INSERT' &&
            table === 'ticket_messages'
          ) {
            const messageRow = newRow as MessageRow;
            if (messageRow.author_id === userDatabaseId) return;
          }

          // NEVER skip ticket INSERT/UPDATE events - all users need to see status changes

          try {
            // Transform data first (async operations)
            let transformedRow: T | null = null;

            if (eventType === 'INSERT' || eventType === 'UPDATE') {
              if (table === 'tickets') {
                const transformed =
                  await realtimeDataService.transformTicketRow(
                    newRow as TicketRow
                  );
                transformedRow = transformed as unknown as T;
              } else if (table === 'ticket_messages') {
                const transformed =
                  await realtimeDataService.transformMessageRow(
                    newRow as MessageRow
                  );
                transformedRow = transformed as unknown as T;
              } else {
                transformedRow = newRow as T;
              }
            }

            // Simple cache update with smart missing ticket handling
            queryClient.setQueryData(stableQueryKey, (oldData: T[] = []) => {
              switch (eventType) {
                case 'INSERT': {
                  if (!transformedRow) return oldData;

                  // Check for duplicates
                  const existingIndex = oldData.findIndex(
                    (item) => item.id === transformedRow.id
                  );
                  if (existingIndex !== -1) {
                    const newData = [...oldData];
                    newData[existingIndex] = transformedRow;
                    return newData;
                  }

                  // Insert new item
                  return table === 'ticket_messages'
                    ? [...oldData, transformedRow] // Messages: append
                    : [transformedRow, ...oldData]; // Tickets: prepend
                }

                case 'UPDATE': {
                  if (!transformedRow) return oldData;

                  const existingIndex = oldData.findIndex(
                    (item) => item.id === transformedRow.id
                  );

                  if (existingIndex !== -1) {
                    // CRITICAL FIX: Update existing ticket (including status changes)
                    // This ensures all users see status updates in real-time
                    const newData = [...oldData];
                    newData[existingIndex] = transformedRow;

                    // For tickets, sort by updated_at to move recently updated to top
                    // Only sort if we have more than 1 item for performance
                    if (table === 'tickets' && newData.length > 1) {
                      return newData.sort((a, b) => {
                        const aUpdated = (a as unknown as Ticket).updatedAt;
                        const bUpdated = (b as unknown as Ticket).updatedAt;
                        return (
                          new Date(bUpdated).getTime() -
                          new Date(aUpdated).getTime()
                        );
                      });
                    }

                    return newData;
                  } else if (table === 'tickets') {
                    // CORE FIX: Ticket doesn't exist in cache but was updated
                    // Add it to the top since it was recently updated
                    const newData = [transformedRow, ...oldData];

                    // Sort by updated_at for proper ordering
                    // Only sort if we have more than 1 item for performance
                    if (newData.length > 1) {
                      return newData.sort((a, b) => {
                        const aUpdated = (a as unknown as Ticket).updatedAt;
                        const bUpdated = (b as unknown as Ticket).updatedAt;
                        return (
                          new Date(bUpdated).getTime() -
                          new Date(aUpdated).getTime()
                        );
                      });
                    }
                    return newData;
                  }

                  // For non-tickets, only update if exists
                  return oldData;
                }

                case 'DELETE':
                  return oldData.filter((item) => item.id !== (oldRow as T).id);

                default:
                  return oldData;
              }
            });

            // Simple missing ticket handling with rate limiting
            if (
              eventType === 'UPDATE' &&
              table === 'tickets' &&
              !transformedRow &&
              newRow
            ) {
              const ticketRow = newRow as TicketRow;
              const tenantId = ticketRow.tenant_id;
              const ticketId = ticketRow.id;

              // Check if ticket is in current cache
              const currentData = queryClient.getQueryData(stableQueryKey) as
                | T[]
                | undefined;
              const ticketInCache = currentData?.some(
                (item) => item.id === ticketId
              );

              if (!ticketInCache && canFetchMissingTicket(ticketId)) {
                trackMissingTicketFetch(ticketId);

                // Simple fetch with timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

                fetch(`/api/tickets/${ticketId}?tenant_id=${tenantId}`, {
                  signal: controller.signal,
                })
                  .then((response) => {
                    clearTimeout(timeoutId);
                    return response.ok ? response.json() : null;
                  })
                  .then((data) => {
                    if (data?.data) {
                      const fetchedTicket = data.data;

                      // Add to cache with proper sorting
                      queryClient.setQueryData(
                        stableQueryKey,
                        (oldData: T[] = []) => {
                          const stillMissing = !oldData.some(
                            (item) => item.id === ticketId
                          );

                          if (stillMissing) {
                            const newData = [
                              fetchedTicket as unknown as T,
                              ...oldData,
                            ];

                            // Sort by updated_at for proper ordering
                            // Only sort if we have more than 1 item for performance
                            if (table === 'tickets' && newData.length > 1) {
                              return newData.sort((a, b) => {
                                const aUpdated = (a as unknown as Ticket)
                                  .updatedAt;
                                const bUpdated = (b as unknown as Ticket)
                                  .updatedAt;
                                return (
                                  new Date(bUpdated).getTime() -
                                  new Date(aUpdated).getTime()
                                );
                              });
                            }

                            return newData;
                          }

                          return oldData;
                        }
                      );
                    }
                  })
                  .catch((error) => {
                    clearTimeout(timeoutId);
                    if (error.name !== 'AbortError') {
                      console.warn(
                        `⚠️ Failed to fetch missing ticket ${ticketId}:`,
                        error
                      );
                    }
                  });
              }
            }
          } catch (error) {
            console.error('Real-time update error:', error);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [
    supabase,
    stableQueryKey,
    table,
    stableOptions,
    user,
    userDatabaseId,
    queryClient,
    realtimeDataService,
  ]);

  return query;
}

// Types needed for tickets
interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
  email?: string;
}

interface TicketFilterOptions {
  status?: string;
  priority?: string;
  assignedTo?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  roleFilter?: 'new' | 'assigned' | 'all';
}

/**
 * Hook to resolve tenant UUID from subdomain
 */
export function useTenantUuid(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useQuery({
    queryKey: ['tenant-uuid', tenantId],
    queryFn: async () => {
      // If already a UUID, return it
      if (
        tenantId.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        )
      ) {
        return tenantId;
      }

      // Convert subdomain to UUID
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenantId)
        .single();

      if (tenantError || !tenantData) {
        throw new Error(`Tenant '${tenantId}' not found`);
      }

      return tenantData.id;
    },
    staleTime: 1000 * 60 * 60, // 1 hour - tenant UUIDs rarely change
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
    enabled: !!tenantId,
  });
}

/**
 * Specialized hook for tickets with unified real-time updates
 * Compatible with useTickets signature for drop-in replacement
 * Uses centralized subscription manager for data consistency
 */
export function useRealtimeTickets(
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) {
  // Note: Using API route instead of direct Supabase client for proper authentication
  const { getToken } = useClerkAuth();
  const { isLoggingOut } = useAuth();
  const { enabled = true, ...filterOptions } = options || {};

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(context.tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Memoize filterOptions to prevent subscription churn
  const stableFilterOptions = useMemo(() => filterOptions, [filterOptions]);

  // Extract complex expression for dependency array
  const stableFilterOptionsString = JSON.stringify(stableFilterOptions || {});

  // Memoize queryKey to prevent subscription churn
  const queryKey = useMemo(
    () => [
      'tickets',
      tenantUuid || 'loading',
      'list',
      stableFilterOptionsString,
    ],
    [tenantUuid, stableFilterOptionsString]
  );

  // Use standard React Query without real-time subscription (handled by unified manager)
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      // CRITICAL FIX: Use API route instead of direct Supabase client to ensure proper authentication
      const searchParams = new URLSearchParams({
        tenant_id: tenantUuid,
      });

      // Add role-based filtering parameters
      if (stableFilterOptions?.roleFilter) {
        searchParams.append('role_filter', stableFilterOptions.roleFilter);
      }

      // Add status filtering
      if (
        stableFilterOptions?.status &&
        Array.isArray(stableFilterOptions.status)
      ) {
        searchParams.append('status', stableFilterOptions.status.join(','));
      } else if (stableFilterOptions?.status) {
        searchParams.append('status', stableFilterOptions.status);
      }

      // CRITICAL FIX: Enhanced authentication token handling with multiple retry strategies
      let token: string | null = null;
      try {
        // First attempt: Get token with supabase template
        token = await getToken({ template: 'supabase' });

        // Second attempt: Try without cache if first fails
        if (!token) {
          token = await getToken({ template: 'supabase', skipCache: true });
        }

        // Third attempt: Try default template as fallback
        if (!token) {
          token = await getToken();
        }

        // Final attempt: Wait a bit and try again (session might be initializing)
        if (!token) {
          await new Promise((resolve) => setTimeout(resolve, 500));
          token = await getToken({ template: 'supabase' });
        }
      } catch (error) {
        console.error('❌ Error getting authentication token:', error);
        // Continue without token - let the API handle the auth error gracefully
      }

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn('⚠️ No authentication token available for API request');
      }

      const response = await fetch(`/api/tickets?${searchParams.toString()}`, {
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Error fetching tickets:', errorData);
        throw new Error(errorData.error || 'Failed to fetch tickets');
      }

      const data = await response.json();
      const tickets = data || []; // API returns tickets array directly, not wrapped in data property

      // API route already returns properly formatted tickets, no transformation needed
      return tickets;
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 1000 * 60 * 30, // 30 minutes
    enabled:
      enabled && !!tenantUuid && !tenantUuidQuery.isLoading && !isLoggingOut,
  });

  // Use unified real-time subscription for automatic cache updates
  useUnifiedRealtimeSubscription(context.tenantId);

  return query;
}

/**
 * Interface for test entries
 */
export interface TestEntry {
  id: string;
  title: string;
  description: string;
  created_at: string;
  tenant_id: string;
}

/**
 * Specialized hook for the test table
 */
export function useRealtimeTest(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useRealtimeQuery<TestEntry>(
    ['realtime-test', tenantId],
    async () => {
      // Use type assertion to bypass TypeScript schema inference issues
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('realtime_test')
        .select('id, title, description, created_at, tenant_id')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching test entries:', error);
        throw error;
      }

      return (data || []) as TestEntry[];
    },
    'realtime_test',
    {
      filter: `tenant_id=eq.${tenantId}`,
      queryOptions: {
        staleTime: 0, // Always fresh for testing
        gcTime: 1000 * 60 * 5, // 5 minutes
      },
    }
  );
}
