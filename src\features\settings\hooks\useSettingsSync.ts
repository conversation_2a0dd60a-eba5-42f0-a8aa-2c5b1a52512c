'use client';

import { useTheme } from 'next-themes';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useCallback, useEffect, useRef } from 'react';
import { AutoAssignmentRule } from '../models/settings.schema';
import {
  useSettingsBroadcast,
  useSettingsRealtime,
} from '../services/settings-realtime.service';
import {
  useSettings,
  useUpdateUserSettings,
  useUpdateAdminSettings,
} from '@/hooks/useSettings';
import { useClerkSupabaseSync } from '@/hooks/useClerkSupabaseSync';
import { getDomainFromWindow } from '@/lib/domain';
// Settings store temporarily disabled for SSR compatibility
import { useSettingsStore } from '../store/use-settings-store';

// Type for department rules update (matches AdminSettingsUpdate schema)
type DepartmentRuleUpdate = {
  department: 'sales' | 'support' | 'marketing' | 'technical';
  assigned_agent_id: string | null;
  is_active: boolean;
  priority?: number;
};

/**
 * Hook for syncing settings with theme provider and handling real-time updates
 */
export function useSettingsSync() {
  const { tenantId, isSignedIn, user } = useAuth();
  const { setTheme } = useTheme();
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const userId = user?.id;

  // Get sync status to prevent race conditions
  const syncTenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;
  const syncStatus = useClerkSupabaseSync(syncTenantId);

  // Modern React Query hook - replaces legacy store
  // Wait for sync completion to prevent race conditions
  const settingsQuery = useSettings(
    tenantId || '',
    userId || '',
    isSignedIn &&
      !!tenantId &&
      !!userId &&
      syncStatus.isComplete &&
      !syncStatus.isLoading
  );

  // Extract user settings from React Query data
  const userSettings = settingsQuery.data?.data?.user_settings || null;
  const isLoading = settingsQuery.isLoading;

  // Set up real-time synchronization
  const realtimeStatus = useSettingsRealtime(tenantId || '', userId || '');

  // Set up cross-tab synchronization
  const { broadcastChange } = useSettingsBroadcast();

  // Sync theme with settings
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ((userSettings as any)?.theme_preference) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setTheme((userSettings as any).theme_preference);
    }
  }, [userSettings, setTheme]);

  // React Query automatically loads settings when enabled conditions are met
  // No manual loading needed - React Query handles this automatically

  // Optimized periodic sync as fallback (minimal frequency due to real-time updates)
  useEffect(() => {
    if (!isSignedIn || !tenantId || !userId) {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
      return;
    }

    // Sync every 10 minutes if data is stale (minimal fallback for real-time)
    // React Query handles staleness automatically, so this is only for edge cases
    syncIntervalRef.current = setInterval(() => {
      if (settingsQuery.isStale) {
        settingsQuery.refetch();
      }
    }, 600000); // 10 minutes

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
    };
  }, [isSignedIn, tenantId, userId, settingsQuery]);

  // Manual refresh function
  const refreshSettings = useCallback(() => {
    if (tenantId && userId) {
      settingsQuery.refetch();
    }
  }, [tenantId, userId, settingsQuery]);

  return {
    refreshSettings,
    isLoading,
    userSettings,
    realtimeStatus,
    broadcastChange,
  };
}

/**
 * Hook for managing settings form state with optimistic updates
 */
export function useSettingsForm() {
  const { tenantId, user } = useAuth();
  const userId = user?.id;

  // Get sync status to prevent race conditions
  const syncTenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;
  const syncStatus = useClerkSupabaseSync(syncTenantId);

  // React Query hooks for data and mutations
  const settingsQuery = useSettings(
    tenantId || '',
    userId || '',
    !!tenantId && !!userId && syncStatus.isComplete && !syncStatus.isLoading
  );
  const updateUserMutation = useUpdateUserSettings(
    tenantId || '',
    userId || ''
  );
  const updateAdminMutation = useUpdateAdminSettings(
    tenantId || '',
    userId || ''
  );

  // Extract data from React Query
  const userSettings = settingsQuery.data?.data?.user_settings || null;
  const adminSettings = settingsQuery.data?.data?.admin_settings || null;
  const isLoading =
    settingsQuery.isLoading ||
    updateUserMutation.isPending ||
    updateAdminMutation.isPending;
  const error =
    settingsQuery.error?.message ||
    updateUserMutation.error?.message ||
    updateAdminMutation.error?.message ||
    null;

  const { broadcastChange } = useSettingsBroadcast();

  const updateTheme = useCallback(
    async (theme: 'light' | 'dark' | 'system') => {
      await updateUserMutation.mutateAsync({ theme_preference: theme });
      broadcastChange();
    },
    [updateUserMutation, broadcastChange]
  );

  const updatePreferences = useCallback(
    async (preferences: Record<string, unknown>) => {
      await updateUserMutation.mutateAsync({ preferences });
      broadcastChange();
    },
    [updateUserMutation, broadcastChange]
  );

  const updateDefaultAgent = useCallback(
    async (agentId: string | null) => {
      await updateAdminMutation.mutateAsync({
        default_agent_settings: {
          default_agent_id: agentId,
          is_active: agentId !== null,
        },
      });
      broadcastChange();
    },
    [updateAdminMutation, broadcastChange]
  );

  const updateAutoAssignmentRules = useCallback(
    async (rules: AutoAssignmentRule[]) => {
      await updateAdminMutation.mutateAsync({
        auto_assignment_rules: rules,
      });
      broadcastChange();
    },
    [updateAdminMutation, broadcastChange]
  );

  return {
    userSettings,
    adminSettings,
    isLoading,
    error,
    updateTheme,
    updatePreferences,
    updateDefaultAgent,
    updateAutoAssignmentRules,
  };
}

/**
 * Hook for settings UI state management
 */
export function useSettingsUI() {
  // Use the working Zustand store
  const isSettingsOpen = useSettingsStore((state) => state.isSettingsOpen);
  const activeSection = useSettingsStore((state) => state.activeSection);
  const openSettings = useSettingsStore((state) => state.openSettings);
  const closeSettings = useSettingsStore((state) => state.closeSettings);
  const setActiveSection = useSettingsStore((state) => state.setActiveSection);

  const openSection = useCallback(
    (section: string) => {
      openSettings(section);
    },
    [openSettings]
  );

  return {
    isSettingsOpen,
    activeSection,
    openSettings,
    closeSettings,
    setActiveSection,
    openSection,
  };
}

/**
 * Hook for admin-specific settings functionality
 */
export function useAdminSettings() {
  const { isAdmin, isSuperAdmin, tenantId, user } = useAuth();
  const userId = user?.id;

  // Use React Query for admin settings
  const settingsQuery = useSettings(
    tenantId || '',
    userId || '',
    !!tenantId && !!userId
  );
  const updateAdminMutation = useUpdateAdminSettings(
    tenantId || '',
    userId || ''
  );

  const adminSettings = settingsQuery.data?.data?.admin_settings || null;
  const isLoading = settingsQuery.isLoading || updateAdminMutation.isPending;

  const hasAdminAccess = isAdmin || isSuperAdmin;

  const updateDefaultAgent = useCallback(
    async (agentId: string | null) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      await updateAdminMutation.mutateAsync({
        default_agent_id: agentId,
        default_agent_is_active: agentId !== null,
      });
    },
    [hasAdminAccess, updateAdminMutation]
  );

  const updateDefaultAgentStatus = useCallback(
    async (agentId: string | null, isActive: boolean) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      await updateAdminMutation.mutateAsync({
        default_agent_id: agentId,
        default_agent_is_active: isActive,
      });
    },
    [hasAdminAccess, updateAdminMutation]
  );

  // New separate functions for clearer operation distinction
  const assignDefaultAgent = useCallback(
    async (agentId: string) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      // Assign agent with default active state (true)
      await updateAdminMutation.mutateAsync({
        default_agent_id: agentId,
        default_agent_is_active: true,
      });
    },
    [hasAdminAccess, updateAdminMutation]
  );

  const removeDefaultAgent = useCallback(async () => {
    if (!hasAdminAccess) {
      throw new Error('Insufficient permissions');
    }
    // Remove agent by setting to null (don't set is_active to false)
    await updateAdminMutation.mutateAsync({
      default_agent_id: null,
    });
  }, [hasAdminAccess, updateAdminMutation]);

  const toggleDefaultAgentStatus = useCallback(
    async (isActive: boolean) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      // Only update the active status, don't change agent assignment
      await updateAdminMutation.mutateAsync({
        default_agent_is_active: isActive,
      });
    },
    [hasAdminAccess, updateAdminMutation]
  );

  const updateDepartmentRules = useCallback(
    async (rules: DepartmentRuleUpdate[]) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      await updateAdminMutation.mutateAsync({
        auto_assignment_rules: rules,
      });
    },
    [hasAdminAccess, updateAdminMutation]
  );

  return {
    hasAdminAccess,
    adminSettings,
    // Legacy functions (kept for backward compatibility)
    updateDefaultAgent,
    updateDefaultAgentStatus,
    // New specific functions for clearer operation distinction
    assignDefaultAgent,
    removeDefaultAgent,
    toggleDefaultAgentStatus,
    updateDepartmentRules,
    isLoading,
  };
}
