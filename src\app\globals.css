@import 'tailwindcss';
@import 'tw-animate-css';
@import '../styles/toast.css';
@import 'tailwindcss';
@plugin "@tailwindcss/forms";
@plugin "@tailwindcss/typography";

@custom-variant dark (&:where(.dark, .dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

@layer base {
  * {
    @apply border-border;
  }
  html,
  body {
    height: 100%;
    overflow: hidden;
    @apply bg-background text-foreground;
  }
  span[data-slate-placeholder] {
    top: 3px !important;
  }

  blockquote span[data-slate-placeholder] {
    top: 11px !important;
  }

  div[data-state='open'] div .space-y-0 {
    height: 0 !important;
  }

  #__next {
    height: 100%;
  }

  .custom-dropdowns button {
    color: var(--foreground) !important;
    background-color: color-mix(
      in oklab,
      var(--input) 20%,
      transparent
    ) !important;
    padding-inline: calc(var(--spacing) * 3) !important;
  }

  /* .custom-dropdowns button span[data-slot='select-value']::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: var(--foreground) !important;
    border-radius: 100%;
    vertical-align: middle;
  } */

  /* Change the arrow SVG color to black */
  .custom-dropdowns button svg {
    stroke: var(--foreground) !important;
    color: var(--foreground) !important;
  }

  blockquote u,
  blockquote strong,
  blockquote em {
    color: var(--text-muted-foreground) !important;
  }

  /* Remove all outlines from inputs and interactive elements */
  input,
  textarea,
  select,
  button,
  [role='button'],
  [tabindex] {
    outline: none !important;
    box-shadow: none !important;
  }

  input:focus,
  textarea:focus,
  select:focus,
  button:focus,
  [role='button']:focus,
  [tabindex]:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Custom no-focus classes for specific form fields */
  .no-focus-ring {
    outline: none !important;
    box-shadow: none !important;
  }

  .no-focus-ring:focus,
  .no-focus-ring:focus-visible,
  .no-focus-ring:focus-within {
    outline: none !important;
    box-shadow: none !important;
    ring: none !important;
  }

  /* Ensure child elements also don't show focus rings */
  .no-focus-ring input,
  .no-focus-ring textarea,
  .no-focus-ring [contenteditable] {
    outline: none !important;
    box-shadow: none !important;
  }

  .no-focus-ring input:focus,
  .no-focus-ring input:focus-visible,
  .no-focus-ring textarea:focus,
  .no-focus-ring textarea:focus-visible,
  .no-focus-ring [contenteditable]:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Rich Content Formatting Styles */

  .slate-empty-paragraph {
    margin: 0 !important;
  }

  .rich-content {
    color: var(--foreground);
  }

  /* Headings */
  .rich-content h1 {
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: 1rem;
    margin-top: 1.5rem;
  }

  .rich-content h1:first-child {
    margin-top: 0;
  }

  .rich-content h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.75rem;
    margin-top: 1.25rem;
  }

  .rich-content h2:first-child {
    margin-top: 0;
  }

  .rich-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.5rem;
    margin-top: 1rem;
  }

  .rich-content h3:first-child {
    margin-top: 0;
  }

  .rich-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;
  }

  .rich-content h4:first-child {
    margin-top: 0;
  }

  .rich-content h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;
  }

  .rich-content h5:first-child {
    margin-top: 0;
  }

  .rich-content h6 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--foreground);
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;
  }

  .rich-content h6:first-child {
    margin-top: 0;
  }

  /* Paragraphs */
  .rich-content p {
    color: var(--foreground);
    margin-bottom: 0.75rem;
    white-space: pre-wrap;
  }

  .rich-content p:last-child {
    margin-bottom: 0;
  }

  /* Text formatting */
  .rich-content strong,
  .rich-content b {
    font-weight: 700;
    color: var(--foreground);
  }

  .rich-content em,
  .rich-content i {
    font-style: italic;
    color: var(--foreground);
  }

  .rich-content u {
    text-decoration: underline;
    text-decoration-color: currentColor;
    color: var(--foreground);
  }

  /* Lists */
  .rich-content ul {
    list-style-type: disc;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--foreground);
  }

  .rich-content ol {
    list-style-type: decimal;
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--foreground);
  }

  .rich-content li {
    margin-bottom: 0.25rem;
    color: var(--foreground);
  }

  .rich-content ul ul,
  .rich-content ol ol,
  .rich-content ul ol,
  .rich-content ol ul {
    margin-bottom: 0;
    margin-top: 0.25rem;
  }

  /* Blockquotes - ensure consistent styling between editor and display */
  .rich-content blockquote {
    border-left: 4px solid hsl(var(--border));
    padding-left: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin: 0.5rem 0;
    color: hsl(var(--muted-foreground));
    font-style: italic;
  }

  /* Support for blockquotes with inline styles (from editor) */
  .rich-content blockquote[style*='border-left'] {
    /* Let inline border-left styles take precedence */
  }

  .rich-content blockquote[style*='color'] {
    /* Let inline color styles take precedence */
  }

  /* Links */
  .rich-content a {
    color: rgb(37 99 235);
    text-decoration: underline;
  }

  .rich-content a:hover {
    color: rgb(29 78 216);
  }

  .dark .rich-content a {
    color: rgb(96 165 250);
  }

  .dark .rich-content a:hover {
    color: rgb(147 197 253);
  }

  /* Spans with color styling - preserve inline color styles from the editor */

  /* Ensure proper spacing for nested elements */
  .rich-content > *:first-child {
    margin-top: 0;
  }

  .rich-content > *:last-child {
    margin-bottom: 0;
  }

  p.empty-paragraph {
    margin: -6px;
  }
}
