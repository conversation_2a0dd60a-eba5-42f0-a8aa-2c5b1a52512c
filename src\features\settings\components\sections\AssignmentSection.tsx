'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { DepartmentAssignmentRules } from '../DepartmentAssignmentRules';

export function AssignmentSection() {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>Auto Assignment</h2>
        <p className='text-sm text-muted-foreground'>
          Configure automatic ticket assignment rules by department.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Department Assignment Rules</CardTitle>
          <CardDescription>
            Set specific agents for each department. These rules override the
            default agent setting.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DepartmentAssignmentRules />
        </CardContent>
      </Card>
    </div>
  );
}
