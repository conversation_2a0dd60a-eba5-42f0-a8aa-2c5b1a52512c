import { toast } from 'sonner';

// Error detail types for better type safety
export interface ValidationErrorDetail {
  field: string;
  message: string;
  code?: string;
}

export interface HttpErrorDetail {
  status: number;
  statusText?: string;
  url?: string;
}

export interface NetworkErrorDetail {
  type: 'fetch' | 'offline' | 'timeout';
  message: string;
}

export type ErrorDetails =
  | ValidationErrorDetail[]
  | HttpErrorDetail
  | NetworkErrorDetail
  | Error
  | unknown;

export interface SettingsError {
  code: string;
  message: string;
  details?: ErrorDetails;
  recoverable: boolean;
}

// API Error interface for better typing
export interface ApiError extends Error {
  status?: number;
  statusText?: string;
  errors?: ValidationErrorDetail[];
}

export class SettingsErrorHandler {
  private static instance: SettingsErrorHandler;

  private constructor() {}

  static getInstance(): SettingsErrorHandler {
    if (!SettingsErrorHandler.instance) {
      SettingsErrorHandler.instance = new SettingsErrorHandler();
    }
    return SettingsErrorHandler.instance;
  }

  /**
   * Type guard for HTTP errors
   */
  private isHttpError(
    error: unknown
  ): error is { status: number; statusText?: string } {
    return (
      typeof error === 'object' &&
      error !== null &&
      'status' in error &&
      typeof (error as { status: unknown }).status === 'number'
    );
  }

  /**
   * Type guard for validation errors
   */
  private isValidationError(
    error: unknown
  ): error is { errors: ValidationErrorDetail[] } {
    return (
      typeof error === 'object' &&
      error !== null &&
      'errors' in error &&
      Array.isArray((error as { errors: unknown }).errors)
    );
  }

  /**
   * Type guard for Error objects
   */
  private isErrorObject(error: unknown): error is Error {
    return error instanceof Error;
  }

  /**
   * Handles API errors with user-friendly messages
   */
  handleApiError(error: unknown): SettingsError {
    // Network errors
    if (!navigator.onLine) {
      const settingsError: SettingsError = {
        code: 'NETWORK_OFFLINE',
        message:
          'You are offline. Changes will be saved when connection is restored.',
        recoverable: true,
      };
      toast.error(settingsError.message);
      return settingsError;
    }

    // Fetch errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      const settingsError: SettingsError = {
        code: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection and try again.',
        recoverable: true,
      };
      toast.error(settingsError.message);
      return settingsError;
    }

    // HTTP errors - type guard for objects with status property
    if (this.isHttpError(error)) {
      switch (error.status) {
        case 401: {
          const authError: SettingsError = {
            code: 'UNAUTHORIZED',
            message: 'Your session has expired. Please sign in again.',
            recoverable: false,
          };
          toast.error(authError.message);
          return authError;
        }

        case 403: {
          const permissionError: SettingsError = {
            code: 'FORBIDDEN',
            message: 'You do not have permission to perform this action.',
            recoverable: false,
          };
          toast.error(permissionError.message);
          return permissionError;
        }

        case 429: {
          const rateLimitError: SettingsError = {
            code: 'RATE_LIMITED',
            message: 'Too many requests. Please wait a moment and try again.',
            recoverable: true,
          };
          toast.error(rateLimitError.message);
          return rateLimitError;
        }

        case 500: {
          const serverError: SettingsError = {
            code: 'SERVER_ERROR',
            message: 'Server error. Please try again in a few moments.',
            recoverable: true,
          };
          toast.error(serverError.message);
          return serverError;
        }

        default: {
          const httpError: SettingsError = {
            code: 'HTTP_ERROR',
            message: `Request failed with status ${error.status}. Please try again.`,
            recoverable: true,
          };
          toast.error(httpError.message);
          return httpError;
        }
      }
    }

    // Validation errors
    if (this.isValidationError(error)) {
      const validationError: SettingsError = {
        code: 'VALIDATION_ERROR',
        message: 'Please check your input and try again.',
        details: error.errors,
        recoverable: true,
      };
      toast.error(validationError.message);
      return validationError;
    }

    // Generic error
    const genericError: SettingsError = {
      code: 'UNKNOWN_ERROR',
      message: this.isErrorObject(error)
        ? error.message
        : 'An unexpected error occurred. Please try again.',
      details: error,
      recoverable: true,
    };
    toast.error(genericError.message);
    return genericError;
  }

  /**
   * Handles cache errors gracefully
   */
  handleCacheError(): void {
    // Cache errors are non-critical, handled gracefully
  }

  /**
   * Handles real-time connection errors
   */
  handleRealtimeError(): void {
    // Real-time connection errors are handled by fallback polling
  }

  /**
   * Handles form validation errors
   */
  handleValidationError(errors: Record<string, { message?: string }>): void {
    const firstError = Object.values(errors)[0];
    if (firstError?.message) {
      toast.error(firstError.message);
    } else {
      toast.error('Please check your input and try again.');
    }
  }

  /**
   * Shows success message for settings operations
   */
  showSuccess(message: string): void {
    toast.success(message);
  }

  /**
   * Shows info message for settings operations
   */
  showInfo(message: string): void {
    toast.info(message);
  }

  /**
   * Retries an operation with exponential backoff
   */
  async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: unknown;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries) {
          break;
        }

        // Don't retry non-recoverable errors
        const settingsError = this.handleApiError(error);
        if (!settingsError.recoverable) {
          throw error;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Wraps async operations with error handling
   */
  async withErrorHandling<T>(
    operation: () => Promise<T>,
    showSuccessMessage?: string
  ): Promise<T | null> {
    try {
      const result = await operation();
      if (showSuccessMessage) {
        this.showSuccess(showSuccessMessage);
      }
      return result;
    } catch (error) {
      this.handleApiError(error);
      return null;
    }
  }
}

/**
 * Performance monitoring utilities
 */
export class SettingsPerformanceMonitor {
  private static instance: SettingsPerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  private constructor() {}

  static getInstance(): SettingsPerformanceMonitor {
    if (!SettingsPerformanceMonitor.instance) {
      SettingsPerformanceMonitor.instance = new SettingsPerformanceMonitor();
    }
    return SettingsPerformanceMonitor.instance;
  }

  /**
   * Measures operation performance
   */
  async measureOperation<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    const startTime = performance.now();

    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      this.recordMetric(operationName, duration);

      // Monitor slow operations for performance optimization
      if (duration > 1000) {
        // Slow operation detected: ${operationName} took ${duration.toFixed(2)}ms
        // Performance monitoring active for optimization
      }

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordMetric(`${operationName}_error`, duration);
      throw error;
    }
  }

  private recordMetric(name: string, duration: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metrics = this.metrics.get(name)!;
    metrics.push(duration);

    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  /**
   * Gets performance statistics
   */
  getStats(operationName: string): {
    count: number;
    average: number;
    min: number;
    max: number;
  } | null {
    const metrics = this.metrics.get(operationName);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    return {
      count: metrics.length,
      average: metrics.reduce((sum, val) => sum + val, 0) / metrics.length,
      min: Math.min(...metrics),
      max: Math.max(...metrics),
    };
  }

  /**
   * Gets all performance statistics
   */
  getAllStats(): Record<string, unknown> {
    const stats: Record<string, unknown> = {};

    for (const [name] of this.metrics) {
      stats[name] = this.getStats(name);
    }

    return stats;
  }
}
