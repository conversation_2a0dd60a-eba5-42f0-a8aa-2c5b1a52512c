import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  const { userId } = await auth();
  if (!userId)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

  console.log(`🔄 Starting simplified sync for user ${userId}`);

  const { tenantId } = await request.json();
  if (!tenantId)
    return NextResponse.json(
      { error: 'tenantId is required' },
      { status: 400 }
    );

  const clerk = await clerkClient();
  // Get user with organization memberships included
  const clerkUser = await clerk.users.getUser(userId);
  if (!clerkUser)
    return NextResponse.json({ error: 'User not found' }, { status: 404 });

  // Also try to get organization memberships using the organizations API
  const userOrgMemberships = [];
  try {
    const orgs = await clerk.organizations.getOrganizationList();
    for (const org of orgs.data) {
      try {
        const membershipList =
          await clerk.organizations.getOrganizationMembershipList({
            organizationId: org.id,
          });
        // Find membership for this user
        const membership = membershipList.data.find(
          (m) => m.publicUserData?.userId === userId
        );
        if (membership) {
          userOrgMemberships.push(membership);
        }
      } catch {
        // User is not a member of this organization, continue
      }
    }
  } catch (error) {
    console.log(`🔄 Could not fetch org memberships via orgs API:`, error);
  }

  try {
    // Use service client to bypass RLS for sync operations
    const serviceSupabase = createServiceSupabaseClient();

    // 1. Create or get tenant
    let tenant;
    const { data: existingTenant } = await serviceSupabase
      .from('tenants')
      .select('*')
      .eq('subdomain', tenantId)
      .single();

    if (existingTenant) {
      tenant = existingTenant;
      console.log(`🔄 Tenant '${tenantId}' already exists`);
    } else {
      const { data: newTenant, error: tenantError } = await serviceSupabase
        .from('tenants')
        .insert({
          name: tenantId.charAt(0).toUpperCase() + tenantId.slice(1),
          subdomain: tenantId,
          status: 'active',
        })
        .select()
        .single();

      if (tenantError) {
        // Handle duplicate key error gracefully
        if (tenantError.code === '23505') {
          console.log(`🔄 Tenant '${tenantId}' already exists`);
          // Fetch existing tenant
          const { data: existingTenantData } = await serviceSupabase
            .from('tenants')
            .select('*')
            .eq('subdomain', tenantId)
            .single();
          tenant = existingTenantData;
        } else {
          console.error('🔄 Failed to create tenant:', tenantError);
          return NextResponse.json(
            { error: 'Failed to create tenant' },
            { status: 500 }
          );
        }
      } else {
        tenant = newTenant;
        console.log(`🔄 Created tenant '${tenantId}'`);
      }
    }

    // 2. Create or update user
    const { data: existingUser } = await serviceSupabase
      .from('users')
      .select('*')
      .eq('clerk_id', userId)
      .single();

    // Get role from Clerk user object with proper mapping
    const email = clerkUser.primaryEmailAddress?.emailAddress || '';
    let clerkRole = 'user'; // default fallback

    // Try multiple approaches to get the role
    try {
      // Try to get role from Clerk organization memberships
      if (userOrgMemberships && userOrgMemberships.length > 0) {
        const orgRole = userOrgMemberships[0]?.role;
        if (orgRole) {
          clerkRole = orgRole;
          console.log(`🔄 User ${email} has org role: ${clerkRole}`);
        }
      } else {
        // Fallback: Use email mapping temporarily (will be replaced with proper Clerk roles)
        if (email === '<EMAIL>') clerkRole = 'org:super_admin';
        else if (email === '<EMAIL>') clerkRole = 'org:agent';
        else if (email === '<EMAIL>') clerkRole = 'org:admin';
        else if (email === '<EMAIL>')
          clerkRole = 'org:agent'; // Test: Pawan should be agent
        console.log(
          `🔄 User ${email} assigned role via email mapping: ${clerkRole}`
        );
      }
    } catch (error) {
      console.log(`🔄 Error getting role, using default: ${clerkRole}`, error);
    }

    // Map Clerk role to database-compatible role
    const roleMapping: Record<string, string> = {
      // Official Clerk organization role keys
      'org:super_admin': 'super_admin',
      'org:admin': 'admin',
      'org:agent': 'agent',
      'org:member': 'user',

      // Fallback for display names or other variations
      'super admin': 'super_admin',
      super_admin: 'super_admin',
      admin: 'admin',
      agent: 'agent',
      member: 'user',
      user: 'user',
    };

    // Normalize the role from Clerk to ensure it's a key in our map
    const role = roleMapping[clerkRole.toLowerCase()] || 'user';
    console.log(`🔄 Mapped role '${clerkRole}' to database role '${role}'`);

    const userData = {
      clerk_id: userId,
      email,
      first_name: clerkUser.firstName || '',
      last_name: clerkUser.lastName || '',
      role,
      status: 'active',
      tenant_id: tenant?.id || '',
      // Always sync the latest avatar URL from Clerk to ensure fresh images
      avatar_url: clerkUser.imageUrl || null,
      last_login_at: new Date().toISOString(),
      created_at: new Date(clerkUser.createdAt || Date.now()).toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (existingUser) {
      const { data: _updatedUser, error: updateError } = await serviceSupabase
        .from('users')
        .update(userData)
        .eq('id', existingUser.id)
        .select()
        .single();

      if (updateError) {
        console.error('🔄 Failed to update user:', updateError);
        return NextResponse.json(
          { error: 'Failed to update user' },
          { status: 500 }
        );
      }

      // user = updatedUser; // User data updated successfully
      console.log(`🔄 Updated user ${userId}`);
    } else {
      const { data: _newUser, error: userError } = await serviceSupabase
        .from('users')
        .insert(userData)
        .select()
        .single();

      if (userError) {
        // Handle duplicate key error gracefully
        if (userError.code === '23505') {
          console.log(
            `🔄 User ${userId} already exists, fetching existing user`
          );
          const { data: _existingUserData } = await serviceSupabase
            .from('users')
            .select('*')
            .eq('clerk_id', userId)
            .single();
          // user = existingUserData; // User already exists
        } else {
          console.error('🔄 Failed to create user:', userError);
          return NextResponse.json(
            { error: 'Failed to create user' },
            { status: 500 }
          );
        }
      } else {
        // user = newUser; // User created successfully
        console.log(`🔄 Created user ${userId}`);
      }
    }

    console.log(
      `🔄 Sync completed successfully for tenant '${tenantId}' and user '${userId}'`
    );
  } catch (error) {
    console.error('🔄 Sync failed with error:', error);
    return NextResponse.json({ error: 'Sync failed' }, { status: 500 });
  }

  return NextResponse.json({
    success: true,
    message: 'Synchronization completed successfully',
  });
}

export async function GET(request: NextRequest) {
  const { userId } = await auth();
  if (!userId)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

  const { searchParams } = new URL(request.url);
  const tenantId = searchParams.get('tenant_id');
  if (!tenantId)
    return NextResponse.json(
      { error: 'tenant_id is required' },
      { status: 400 }
    );

  try {
    const serviceSupabase = createServiceSupabaseClient();

    // Check if tenant exists
    const { data: tenant } = await serviceSupabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenantId)
      .single();

    // Check if user exists
    const { data: user } = await serviceSupabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single();

    const syncStatus = {
      needsSync: !tenant || !user,
      tenantExists: !!tenant,
      userExists: !!user,
    };

    console.log(`🔄 Sync status check for ${userId}/${tenantId}:`, syncStatus);

    return NextResponse.json({ success: true, syncStatus });
  } catch (error) {
    console.error('🔄 Sync status check failed:', error);
    return NextResponse.json(
      { error: 'Sync status check failed' },
      { status: 500 }
    );
  }
}
