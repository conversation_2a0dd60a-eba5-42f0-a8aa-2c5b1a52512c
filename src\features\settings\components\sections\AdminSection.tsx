'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { DefaultAgentSelector } from '../DefaultAgentSelector';

export function AdminSection() {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>Administration</h2>
        <p className='text-sm text-muted-foreground'>
          Manage system-wide settings and configurations.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Default Agent Assignment</CardTitle>
          <CardDescription>
            Set the default agent for new tickets when no department-specific
            rules apply.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DefaultAgentSelector />
        </CardContent>
      </Card>
    </div>
  );
}
