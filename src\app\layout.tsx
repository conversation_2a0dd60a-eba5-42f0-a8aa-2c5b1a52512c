import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import { ClerkProvider } from '@clerk/nextjs';
import { ThemeProvider } from 'next-themes';
import { SessionValidator } from '@/features/shared/components/SessionValidator';
import { SupabaseProvider } from '@/features/shared/components/SupabaseProvider';
import { TenantInitializer } from '@/features/tenant/components/TenantInitializer';
import { Toaster } from '@/features/shared/components/ui/sonner';
import { NoSSR } from '@/features/shared/components/ui/no-ssr';
import ReactQueryProvider from '@/providers/ReactQueryProvider';

import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'QuantumNest',
  description: 'Modern support ticketing system built with Next.js',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang='en' suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning
        >
          <ReactQueryProvider>
            <SupabaseProvider>
              <NoSSR>
                <ThemeProvider
                  attribute='class'
                  defaultTheme='system'
                  storageKey='ticketing-theme'
                  enableSystem
                  disableTransitionOnChange
                >
                  <TenantInitializer />
                  <SessionValidator>{children}</SessionValidator>
                  <Toaster
                    position='top-right'
                    expand={false}
                    richColors={false}
                    closeButton={false}
                  />
                </ThemeProvider>
              </NoSSR>
            </SupabaseProvider>
          </ReactQueryProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
