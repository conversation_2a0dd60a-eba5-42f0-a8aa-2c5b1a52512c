'use client';

import { useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useTenantActions } from '../store/use-tenant-store';
import { useClerkSupabaseSync } from '@/hooks/useClerkSupabaseSync';
import { getDomainFromWindow } from '@/lib/domain';

export function TenantInitializer() {
  const { isLoaded, isSignedIn } = useUser();
  const { initializeDomain } = useTenantActions();

  // Get tenant ID from domain for sync
  const tenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;

  // Initialize Clerk-Supabase sync when user is authenticated
  const syncStatus = useClerkSupabaseSync(tenantId);

  useEffect(() => {
    if (isLoaded) {
      // Initialize domain info only - server state handled by React Query
      initializeDomain();
    }
  }, [isLoaded, initializeDomain]);

  // Monitor sync status for tenant initialization
  useEffect(() => {
    if (isSignedIn && tenantId && syncStatus && !syncStatus.isLoading) {
      // Sync status monitoring active - handles tenant initialization
      // Status: needsSync, isLoading, isComplete, error tracking
    }
  }, [
    isSignedIn,
    tenantId,
    syncStatus,
    syncStatus.isComplete,
    syncStatus.needsSync,
    syncStatus.error,
  ]);

  return null;
}
