import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';
// Legacy cache service removed - React Query handles caching

type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];

class RealtimeDataService {
  private supabase: SupabaseClient<Database>;
  // Manual attachment cache removed - React Query handles caching

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  private async getTenantSubdomain(tenantUuid: string): Promise<string> {
    // Direct Supabase query - React Query will handle caching at component level
    const { data, error } = await this.supabase
      .from('tenants')
      .select('subdomain')
      .eq('id', tenantUuid)
      .single();

    if (error || !data) {
      return tenantUuid;
    }

    return data.subdomain;
  }

  private async getUserInfo(userId: string): Promise<{
    id: string;
    name: string;
    email: string;
    role: string;
    clerkId?: string;
    avatarUrl?: string;
  }> {
    try {
      console.log('🔍 RealtimeDataService: Fetching user info for ID:', userId);

      // Direct Supabase query - React Query will handle caching at component level
      const { data, error } = await this.supabase
        .from('users')
        .select('id, first_name, last_name, email, role, clerk_id, avatar_url') // CRITICAL FIX: Include id, role, and avatar_url for assignment metadata
        .eq('id', userId)
        .single();

      if (error) {
        console.error(
          '❌ RealtimeDataService: Error fetching user info:',
          error,
          'for userId:',
          userId
        );
        const fallback = {
          id: userId,
          name: 'Unknown User',
          email: '<EMAIL>',
          role: 'user',
        };
        return fallback;
      }

      if (!data) {
        console.warn(
          '⚠️ RealtimeDataService: No user data found for userId:',
          userId
        );
        const fallback = {
          id: userId,
          name: 'Unknown User',
          email: '<EMAIL>',
          role: 'user',
        };
        return fallback;
      }

      const userInfo = {
        id: data.id,
        name:
          `${data.first_name || ''} ${data.last_name || ''}`.trim() ||
          'Unknown User',
        email: data.email || '<EMAIL>',
        role: data.role || 'user',
        ...(data.clerk_id && { clerkId: data.clerk_id }),
        ...(data.avatar_url && { avatarUrl: data.avatar_url }),
      };

      console.log(
        '✅ RealtimeDataService: Successfully fetched user info:',
        userInfo.name,
        'for userId:',
        userId
      );
      return userInfo;
    } catch (error) {
      console.error(
        '❌ RealtimeDataService: Exception in getUserInfo:',
        error,
        'for userId:',
        userId
      );
      const fallback = {
        id: userId,
        name: 'Unknown User',
        email: '<EMAIL>',
        role: 'user',
      };
      return fallback;
    }
  }

  private async getMessageAttachments(messageId: string): Promise<
    {
      id: string;
      name: string;
      type: string;
      size: number;
      url: string;
      uploadedAt: Date;
    }[]
  > {
    try {
      // Direct Supabase query - React Query will handle caching at component level
      const { data: attachments, error } = await this.supabase
        .from('attachments')
        .select('id, file_name, file_type, file_size, storage_path, created_at')
        .eq('message_id', messageId)
        .order('created_at', { ascending: true });

      if (error) {
        return [];
      }

      const transformedAttachments = (attachments || []).map((att) => ({
        id: att.id,
        name: att.file_name,
        type: att.file_type,
        size: att.file_size,
        url: `/api/attachments/${att.id}`,
        uploadedAt: new Date(att.created_at || new Date()),
      }));

      return transformedAttachments;
    } catch (error) {
      console.error('Failed to fetch message attachments:', error);
      return [];
    }
  }

  public async transformTicketRow(payload: TicketRow): Promise<Ticket> {
    console.log(
      '🔄 RealtimeDataService: Starting transformTicketRow for ticket:',
      payload.id,
      'created_by:',
      payload.created_by
    );

    // CRITICAL PERFORMANCE FIX: Use Promise.allSettled for better error handling and parallel execution
    const promises = [
      this.getTenantSubdomain(payload.tenant_id),
      this.getUserInfo(payload.created_by),
    ];

    // If ticket is assigned, also get the assigned user's info for Clerk ID
    if (payload.assigned_to) {
      console.log(
        '🔄 RealtimeDataService: Ticket is assigned to:',
        payload.assigned_to
      );
      promises.push(this.getUserInfo(payload.assigned_to));
    }

    const results = await Promise.allSettled(promises);

    // Extract results with fallbacks for failed promises to ensure real-time continues working
    const tenantId =
      results[0]?.status === 'fulfilled'
        ? (results[0] as PromiseFulfilledResult<string>).value
        : payload.tenant_id;
    const userInfo =
      results[1]?.status === 'fulfilled'
        ? (
            results[1] as PromiseFulfilledResult<{
              id: string;
              name: string;
              email: string;
              role: string;
              clerkId?: string;
              avatarUrl?: string;
            }>
          ).value
        : {
            id: payload.created_by,
            name: 'Unknown User',
            email: '<EMAIL>',
            role: 'user',
          };
    const assignedUserInfo =
      results[2]?.status === 'fulfilled'
        ? (
            results[2] as PromiseFulfilledResult<{
              id: string;
              name: string;
              email: string;
              role: string;
              clerkId?: string;
              avatarUrl?: string;
            }>
          ).value
        : undefined;

    const transformedTicket = {
      id: payload.id,
      tenantId,
      title: payload.title,
      description: payload.description || '',
      status: payload.status as 'open' | 'closed' | 'resolved' | 'pending',
      priority: payload.priority as 'low' | 'medium' | 'high' | 'urgent',
      department: payload.department as
        | 'sales'
        | 'support'
        | 'marketing'
        | 'technical',
      userId: payload.created_by,
      userName: userInfo.name,
      userEmail: userInfo.email,
      userAvatar: userInfo.avatarUrl || undefined, // CRITICAL FIX: Use cached avatar URL for instant image loading
      createdAt: new Date(payload.created_at || new Date()),
      updatedAt: new Date(payload.updated_at || new Date()),
      messages: [],
      attachments: [],
      assignedTo: payload.assigned_to || undefined,
      assignedToClerkId:
        // CRITICAL FIX: Use database field if available, otherwise fetch from user info
        ((payload as Record<string, unknown>).assigned_to_clerk_id as string) ||
        assignedUserInfo?.clerkId ||
        undefined,
      assignedBy: payload.assigned_to ? payload.created_by : undefined,
      assignedAt: payload.assigned_to
        ? new Date(payload.updated_at || new Date())
        : undefined,
      dueDate: payload.due_date ? new Date(payload.due_date) : undefined,
      resolvedAt: payload.resolved_at
        ? new Date(payload.resolved_at)
        : undefined,
      closedAt: payload.closed_at ? new Date(payload.closed_at) : undefined,
      tags: payload.tags || [],
      metadata: {
        ...(payload.metadata && typeof payload.metadata === 'object'
          ? (payload.metadata as Record<string, unknown>)
          : {}),
        // CRITICAL FIX: Add structured assignment metadata for proper display
        assignedUser: assignedUserInfo
          ? {
              id: assignedUserInfo.id,
              name: assignedUserInfo.name,
              email: assignedUserInfo.email,
              role: assignedUserInfo.role,
            }
          : undefined,
        createdByUser: {
          id: userInfo.id,
          name: userInfo.name,
          email: userInfo.email,
          role: userInfo.role,
        },
        // Add assignment information for proper display
        assignment: payload.assigned_to
          ? {
              assignedByUser: {
                id: userInfo.id,
                name: userInfo.name,
                email: userInfo.email,
                role: userInfo.role,
              },
              assignedAt: payload.updated_at,
              auto_assigned: true, // Assume auto-assigned for realtime updates
            }
          : undefined,
      } as Record<string, unknown>,
    };

    console.log(
      '✅ RealtimeDataService: Completed transformTicketRow for ticket:',
      transformedTicket.id,
      'userName:',
      transformedTicket.userName
    );
    return transformedTicket;
  }

  public async transformMessageRow(
    payload: MessageRow
  ): Promise<TicketMessage> {
    const [userInfo, attachments] = await Promise.allSettled([
      this.getUserInfo(payload.author_id),
      this.getMessageAttachments(payload.id),
    ]);

    const resolvedUserInfo =
      userInfo.status === 'fulfilled'
        ? userInfo.value
        : {
            id: payload.author_id,
            name: 'Unknown User',
            email: '<EMAIL>',
            role: 'user',
          };

    const resolvedAttachments =
      attachments.status === 'fulfilled' ? attachments.value : [];

    if (attachments.status === 'rejected') {
      console.warn('Failed to fetch attachments:', attachments.reason);
    }

    let createdAt: Date;
    try {
      const timestamp = payload.created_at || new Date().toISOString();
      createdAt = new Date(timestamp);
      if (isNaN(createdAt.getTime())) {
        createdAt = new Date();
      }
    } catch {
      createdAt = new Date();
    }

    return {
      id: payload.id,
      content: payload.content || '',
      createdAt,
      authorId: payload.author_id,
      authorName: resolvedUserInfo.name,
      authorAvatar: resolvedUserInfo.avatarUrl,
      ticketId: payload.ticket_id,
      attachments: resolvedAttachments,
    };
  }
}

export default RealtimeDataService;
