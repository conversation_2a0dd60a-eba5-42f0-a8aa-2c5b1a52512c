import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';
// Legacy cache service removed - React Query handles caching

type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];

class RealtimeDataService {
  private supabase: SupabaseClient<Database>;
  // Manual attachment cache removed - React Query handles caching

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  private async getTenantSubdomain(tenantUuid: string): Promise<string> {
    // Direct Supabase query - React Query will handle caching at component level
    const { data, error } = await this.supabase
      .from('tenants')
      .select('subdomain')
      .eq('id', tenantUuid)
      .single();

    if (error || !data) {
      return tenantUuid;
    }

    return data.subdomain;
  }

  private async getUserInfo(userId: string): Promise<{
    id: string;
    name: string;
    email: string;
    role: string;
    clerkId?: string;
    avatarUrl?: string;
  }> {
    try {
      console.log('🔍 RealtimeDataService: Fetching user info for ID:', userId);

      // CRITICAL FIX: Add retry logic to prevent race conditions
      let attempts = 0;
      const maxAttempts = 3;
      let data: {
        id: string;
        first_name: string | null;
        last_name: string | null;
        email: string;
        role: string;
        clerk_id: string;
        avatar_url: string | null;
      } | null = null;
      let error: unknown = null;

      while (attempts < maxAttempts && !data) {
        attempts++;

        // Direct Supabase query with retry
        const result = await this.supabase
          .from('users')
          .select(
            'id, first_name, last_name, email, role, clerk_id, avatar_url'
          )
          .eq('id', userId)
          .single();

        data = result.data;
        error = result.error;

        // If we get data, break early
        if (data) break;

        // Wait before retry (exponential backoff)
        if (attempts < maxAttempts) {
          await new Promise((resolve) =>
            setTimeout(resolve, 100 * Math.pow(2, attempts - 1))
          );
        }
      }

      if (error || !data) {
        console.error(
          '❌ RealtimeDataService: Failed to fetch user after retries:',
          error,
          'for userId:',
          userId
        );

        // CRITICAL FIX: Instead of returning "Unknown User", throw an error
        // This will prevent anonymous users from being created
        throw new Error(
          `User ${userId} not found after ${maxAttempts} attempts`
        );
      }

      const userInfo = {
        id: data.id,
        name:
          `${data.first_name || ''} ${data.last_name || ''}`.trim() ||
          data.email?.split('@')[0] || // Use email prefix as fallback
          `User-${data.id.substring(0, 8)}`, // Use partial ID as last resort
        email: data.email || `user-${data.id}@temp.local`,
        role: data.role || 'user',
        ...(data.clerk_id && { clerkId: data.clerk_id }),
        ...(data.avatar_url && { avatarUrl: data.avatar_url }),
      };

      console.log(
        '✅ RealtimeDataService: Successfully fetched user info:',
        userInfo.name,
        'for userId:',
        userId
      );
      return userInfo;
    } catch (error) {
      console.error(
        '❌ RealtimeDataService: Exception in getUserInfo:',
        error,
        'for userId:',
        userId
      );

      // CRITICAL FIX: Rethrow the error instead of returning fallback
      // This prevents "Unknown User" entries from being created
      throw error;
    }
  }

  private async getMessageAttachments(messageId: string): Promise<
    {
      id: string;
      name: string;
      type: string;
      size: number;
      url: string;
      uploadedAt: Date;
    }[]
  > {
    try {
      // Direct Supabase query - React Query will handle caching at component level
      const { data: attachments, error } = await this.supabase
        .from('attachments')
        .select('id, file_name, file_type, file_size, storage_path, created_at')
        .eq('message_id', messageId)
        .order('created_at', { ascending: true });

      if (error) {
        return [];
      }

      const transformedAttachments = (attachments || []).map((att) => ({
        id: att.id,
        name: att.file_name,
        type: att.file_type,
        size: att.file_size,
        url: `/api/attachments/${att.id}`,
        uploadedAt: new Date(att.created_at || new Date()),
      }));

      return transformedAttachments;
    } catch (error) {
      console.error('Failed to fetch message attachments:', error);
      return [];
    }
  }

  public async transformTicketRow(payload: TicketRow): Promise<Ticket> {
    console.log(
      '🔄 RealtimeDataService: Starting transformTicketRow for ticket:',
      payload.id,
      'created_by:',
      payload.created_by
    );

    // CRITICAL PERFORMANCE FIX: Use Promise.allSettled for better error handling and parallel execution
    const promises = [
      this.getTenantSubdomain(payload.tenant_id),
      this.getUserInfo(payload.created_by),
    ];

    // If ticket is assigned, also get the assigned user's info for Clerk ID
    if (payload.assigned_to) {
      console.log(
        '🔄 RealtimeDataService: Ticket is assigned to:',
        payload.assigned_to
      );
      promises.push(this.getUserInfo(payload.assigned_to));
    }

    const results = await Promise.allSettled(promises);

    // Extract results with error handling - CRITICAL FIX: Skip processing if essential user data is missing
    const tenantId =
      results[0]?.status === 'fulfilled'
        ? (results[0] as PromiseFulfilledResult<string>).value
        : payload.tenant_id;

    // CRITICAL FIX: If getUserInfo fails, skip this ticket transformation to prevent "Unknown User"
    if (results[1]?.status === 'rejected') {
      console.warn(
        '⚠️ RealtimeDataService: Skipping ticket transformation due to user info failure:',
        payload.id,
        'user:',
        payload.created_by
      );
      throw new Error(`Failed to get user info for ticket ${payload.id}`);
    }

    const userInfo = (
      results[1] as PromiseFulfilledResult<{
        id: string;
        name: string;
        email: string;
        role: string;
        clerkId?: string;
        avatarUrl?: string;
      }>
    ).value;

    // CRITICAL FIX: Handle assigned user info failure gracefully
    const assignedUserInfo =
      results[2]?.status === 'fulfilled'
        ? (
            results[2] as PromiseFulfilledResult<{
              id: string;
              name: string;
              email: string;
              role: string;
              clerkId?: string;
              avatarUrl?: string;
            }>
          ).value
        : undefined;

    // If assigned user info failed but ticket is assigned, log warning but continue
    if (payload.assigned_to && results[2]?.status === 'rejected') {
      console.warn(
        '⚠️ RealtimeDataService: Failed to get assigned user info for ticket:',
        payload.id,
        'assigned_to:',
        payload.assigned_to
      );
    }

    const transformedTicket = {
      id: payload.id,
      tenantId,
      title: payload.title,
      description: payload.description || '',
      status: payload.status as 'open' | 'closed' | 'resolved' | 'pending',
      priority: payload.priority as 'low' | 'medium' | 'high' | 'urgent',
      department: payload.department as
        | 'sales'
        | 'support'
        | 'marketing'
        | 'technical',
      userId: payload.created_by,
      userName: userInfo.name,
      userEmail: userInfo.email,
      userAvatar: userInfo.avatarUrl || undefined, // CRITICAL FIX: Use cached avatar URL for instant image loading
      createdAt: new Date(payload.created_at || new Date()),
      updatedAt: new Date(payload.updated_at || new Date()),
      messages: [],
      attachments: [],
      assignedTo: payload.assigned_to || undefined,
      assignedToClerkId:
        // CRITICAL FIX: Use database field if available, otherwise fetch from user info
        ((payload as Record<string, unknown>).assigned_to_clerk_id as string) ||
        assignedUserInfo?.clerkId ||
        undefined,
      assignedBy: payload.assigned_to ? payload.created_by : undefined,
      assignedAt: payload.assigned_to
        ? new Date(payload.updated_at || new Date())
        : undefined,
      dueDate: payload.due_date ? new Date(payload.due_date) : undefined,
      resolvedAt: payload.resolved_at
        ? new Date(payload.resolved_at)
        : undefined,
      closedAt: payload.closed_at ? new Date(payload.closed_at) : undefined,
      tags: payload.tags || [],
      metadata: {
        ...(payload.metadata && typeof payload.metadata === 'object'
          ? (payload.metadata as Record<string, unknown>)
          : {}),
        // CRITICAL FIX: Add structured assignment metadata for proper display
        assignedUser: assignedUserInfo
          ? {
              id: assignedUserInfo.id,
              name: assignedUserInfo.name,
              email: assignedUserInfo.email,
              role: assignedUserInfo.role,
            }
          : undefined,
        createdByUser: {
          id: userInfo.id,
          name: userInfo.name,
          email: userInfo.email,
          role: userInfo.role,
        },
        // Add assignment information for proper display
        assignment: payload.assigned_to
          ? {
              assignedByUser: {
                id: userInfo.id,
                name: userInfo.name,
                email: userInfo.email,
                role: userInfo.role,
              },
              assignedAt: payload.updated_at,
              auto_assigned: true, // Assume auto-assigned for realtime updates
            }
          : undefined,
      } as Record<string, unknown>,
    };

    console.log(
      '✅ RealtimeDataService: Completed transformTicketRow for ticket:',
      transformedTicket.id,
      'userName:',
      transformedTicket.userName
    );
    return transformedTicket;
  }

  public async transformMessageRow(
    payload: MessageRow
  ): Promise<TicketMessage> {
    const [userInfo, attachments] = await Promise.allSettled([
      this.getUserInfo(payload.author_id),
      this.getMessageAttachments(payload.id),
    ]);

    // CRITICAL FIX: If getUserInfo fails, skip this message transformation to prevent "Unknown User"
    if (userInfo.status === 'rejected') {
      console.warn(
        '⚠️ RealtimeDataService: Skipping message transformation due to user info failure:',
        payload.id,
        'author:',
        payload.author_id
      );
      throw new Error(`Failed to get user info for message ${payload.id}`);
    }

    const resolvedUserInfo = userInfo.value;
    const resolvedAttachments =
      attachments.status === 'fulfilled' ? attachments.value : [];

    if (attachments.status === 'rejected') {
      console.warn('Failed to fetch attachments:', attachments.reason);
    }

    let createdAt: Date;
    try {
      const timestamp = payload.created_at || new Date().toISOString();
      createdAt = new Date(timestamp);
      if (isNaN(createdAt.getTime())) {
        createdAt = new Date();
      }
    } catch {
      createdAt = new Date();
    }

    return {
      id: payload.id,
      content: payload.content || '',
      createdAt,
      authorId: payload.author_id,
      authorName: resolvedUserInfo.name,
      authorAvatar: resolvedUserInfo.avatarUrl,
      ticketId: payload.ticket_id,
      attachments: resolvedAttachments,
    };
  }
}

export default RealtimeDataService;
