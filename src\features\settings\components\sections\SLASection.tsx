'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { SLAConfigurationForm } from '../SLAForm';

export function SLASection() {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>SLA Management</h2>
        <p className='text-sm text-muted-foreground'>
          Configure automatic ticket priority escalation based on SLA deadlines.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>SLA Deadline Configuration</CardTitle>
          <CardDescription>
            Set deadline hours for each priority level. Tickets will
            automatically escalate when these deadlines are exceeded.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SLAConfigurationForm />
        </CardContent>
      </Card>
    </div>
  );
}
