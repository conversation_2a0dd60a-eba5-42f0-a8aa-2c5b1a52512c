'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/features/shared/components/ui/button';
import { Input } from '@/features/shared/components/ui/input';
import { Switch } from '@/features/shared/components/ui/switch';
import { Label } from '@/features/shared/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import { toast } from '@/features/shared/components/toast';
import { useAuth } from '@/features/shared/hooks/useAuth';

interface AutoCloseConfigurationData {
  auto_close_enabled: boolean;
  auto_close_days: number;
  auto_close_unit: 'hours' | 'days' | 'weeks';
}

// API functions
async function fetchAutoCloseConfiguration() {
  const response = await fetch('/api/settings/auto-close');

  if (!response.ok) {
    if (response.status === 404) {
      // Return default configuration if none exists
      return {
        auto_close_enabled: true,
        auto_close_days: 7,
        auto_close_unit: 'days' as const,
      };
    }
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch auto-close configuration');
  }

  return response.json();
}

async function updateAutoCloseConfiguration(data: AutoCloseConfigurationData) {
  const response = await fetch('/api/settings/auto-close', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update auto-close configuration');
  }

  return response.json();
}

async function resetAutoCloseConfiguration() {
  const response = await fetch('/api/settings/auto-close', {
    method: 'DELETE',
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to reset auto-close configuration');
  }

  return response.json();
}

export function AutoCloseConfigurationForm() {
  const { tenantId } = useAuth();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState<AutoCloseConfigurationData>({
    auto_close_enabled: true,
    auto_close_days: 7,
    auto_close_unit: 'days',
  });

  // Fetch auto-close configuration
  const { data: autoCloseConfig, isLoading } = useQuery({
    queryKey: ['auto-close-configuration', tenantId],
    queryFn: fetchAutoCloseConfiguration,
    enabled: !!tenantId,
  });

  // Update form data when config loads
  useEffect(() => {
    if (autoCloseConfig) {
      setFormData({
        auto_close_enabled: autoCloseConfig.auto_close_enabled,
        auto_close_days: autoCloseConfig.auto_close_days,
        auto_close_unit: autoCloseConfig.auto_close_unit,
      });
    }
  }, [autoCloseConfig]);

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: updateAutoCloseConfiguration,
    onSuccess: () => {
      const { auto_close_enabled, auto_close_days, auto_close_unit } = formData;

      if (auto_close_enabled) {
        toast.success('Auto-close enabled successfully', {
          description: `Resolved tickets will now be automatically closed after ${auto_close_days} ${auto_close_unit}`,
        });
      } else {
        toast.success('Auto-close disabled successfully', {
          description: 'Tickets will no longer be automatically closed',
        });
      }

      queryClient.invalidateQueries({ queryKey: ['auto-close-configuration'] });
    },
    onError: (error: Error) => {
      toast.error('Error', {
        description: error.message,
      });
    },
  });

  // Reset mutation
  const resetMutation = useMutation({
    mutationFn: resetAutoCloseConfiguration,
    onSuccess: () => {
      toast.success('Auto-close settings reset successfully', {
        description:
          'Auto-close enabled with 7 days duration (default settings restored)',
      });
      queryClient.invalidateQueries({ queryKey: ['auto-close-configuration'] });
    },
    onError: (error: Error) => {
      toast.error('Error', {
        description: error.message,
      });
    },
  });

  const handleInputChange = (
    field: keyof AutoCloseConfigurationData,
    value: number | boolean | string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  const handleReset = () => {
    resetMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className='text-sm text-muted-foreground'>
        Loading auto-close configuration...
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='flex items-center justify-between rounded-lg border p-4'>
        <div className='space-y-0.5'>
          <Label className='text-base'>Enable Auto-Close</Label>
          <p className='text-sm text-muted-foreground'>
            Automatically close resolved tickets after the specified time period
          </p>
        </div>
        <Switch
          checked={formData.auto_close_enabled}
          onCheckedChange={(checked) =>
            handleInputChange('auto_close_enabled', checked)
          }
        />
      </div>

      {formData.auto_close_enabled && (
        <div className='space-y-4'>
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2 flex-1'>
              <Label htmlFor='duration'>Duration</Label>
              <Input
                id='duration'
                type='number'
                min='1'
                max='365'
                value={formData.auto_close_days}
                onChange={(e) =>
                  handleInputChange(
                    'auto_close_days',
                    parseInt(e.target.value) || 1
                  )
                }
                className='w-full'
              />
              <p className='text-sm text-muted-foreground'>
                How long to wait before auto-closing
              </p>
            </div>

            <div className='space-y-2 flex-1'>
              <Label htmlFor='unit'>Time Unit</Label>
              <Select
                value={formData.auto_close_unit}
                onValueChange={(value: 'hours' | 'days' | 'weeks') =>
                  handleInputChange('auto_close_unit', value)
                }
              >
                <SelectTrigger className='w-full'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='hours'>Hours</SelectItem>
                  <SelectItem value='days'>Days</SelectItem>
                  <SelectItem value='weeks'>Weeks</SelectItem>
                </SelectContent>
              </Select>
              <p className='text-sm text-muted-foreground'>
                Time unit for the duration
              </p>
            </div>
          </div>

          <div className='rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4'>
            <p className='text-sm text-blue-800 dark:text-blue-200'>
              <strong>Current Setting:</strong> Resolved tickets will be
              automatically closed after {formData.auto_close_days}{' '}
              {formData.auto_close_unit}
            </p>
          </div>
        </div>
      )}

      <div className='flex gap-3'>
        <Button type='submit' disabled={updateMutation.isPending}>
          {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
        </Button>
        <Button
          type='button'
          variant='outline'
          onClick={handleReset}
          disabled={resetMutation.isPending}
        >
          {resetMutation.isPending ? 'Resetting...' : 'Reset to Default'}
        </Button>
      </div>
    </form>
  );
}
