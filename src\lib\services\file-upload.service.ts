import { UploadedFile } from '@/features/shared/components/FileUpload';

export interface UploadedAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedAt: string;
}

export class FileUploadService {
  /**
   * Upload a single file to Supabase storage
   */
  static async uploadFile(
    file: File,
    tenantId: string,
    ticketId?: string,
    messageId?: string
  ): Promise<UploadedAttachment> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('tenant_id', tenantId);

    if (ticketId) {
      formData.append('ticket_id', ticketId);
    }

    if (messageId) {
      formData.append('message_id', messageId);
    }

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to upload file');
    }

    return response.json();
  }

  /**
   * Upload multiple files and return their attachment IDs
   */
  static async uploadFiles(
    files: UploadedFile[],
    tenantId: string,
    ticketId?: string,
    messageId?: string
  ): Promise<string[]> {
    const uploadPromises = files.map((uploadedFile) =>
      this.uploadFile(uploadedFile.file, tenantId, ticketId, messageId)
    );

    try {
      const attachments = await Promise.all(uploadPromises);
      return attachments.map((attachment) => attachment.id);
    } catch (error) {
      console.error('Failed to upload files:', error);
      throw error;
    }
  }

  /**
   * Upload files for ticket creation (no ticket ID yet)
   */
  static async uploadFilesForTicket(
    files: UploadedFile[],
    tenantId: string
  ): Promise<string[]> {
    return this.uploadFiles(files, tenantId);
  }

  /**
   * Upload files for message reply
   */
  static async uploadFilesForMessage(
    files: UploadedFile[],
    tenantId: string,
    ticketId: string
  ): Promise<string[]> {
    return this.uploadFiles(files, tenantId, ticketId);
  }

  /**
   * Upload files for message reply and return full attachment data
   */
  static async uploadFilesForMessageWithData(
    files: UploadedFile[],
    tenantId: string,
    ticketId: string
  ): Promise<UploadedAttachment[]> {
    const uploadPromises = files.map((uploadedFile) =>
      this.uploadFile(uploadedFile.file, tenantId, ticketId)
    );

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Failed to upload files:', error);
      throw error;
    }
  }
}
