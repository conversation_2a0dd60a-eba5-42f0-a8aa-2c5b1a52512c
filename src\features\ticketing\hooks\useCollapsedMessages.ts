'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

// Import the Attachment type for proper typing
import { Attachment } from '../models/ticket.schema';
import { useModernMessageCache } from '@/hooks/useModernMessageCache';
import { useTenant } from '@/features/tenant/store/use-tenant-store';

// Generic message type for collapsed messages functionality - matches MessageItem type
interface DisplayMessage {
  id: string;
  author: {
    name: string;
    email?: string;
    avatarUrl?: string;
  };
  content: string;
  createdAt: Date;
  attachments?: Attachment[];
  isInternal?: boolean;
  isOptimistic?: boolean;
}

interface UseCollapsedMessagesOptions {
  ticketId: string;
  allMessages: DisplayMessage[];
  isLoadingMessages: boolean;
}

interface UseCollapsedMessagesReturn {
  displayMessages: DisplayMessage[];
  collapsedCount: number;
  isExpanded: boolean;
  isLoadingCollapsed: boolean;
  shouldShowCollapsedIndicator: boolean;
  expandCollapsedMessages: () => void;
}

// Cache for expanded states per ticket - resets on navigation
const expandedStatesCache = new Map<string, boolean>();

// Removed middleMessagesCache - now using simple cache

// Track current ticket to detect navigation
let currentTicketId: string | null = null;

// Legacy cache clearing removed - UI state resets naturally on logout

export function useCollapsedMessages({
  ticketId,
  allMessages,
  isLoadingMessages,
}: UseCollapsedMessagesOptions): UseCollapsedMessagesReturn {
  const { tenantId } = useTenant();

  // Modern cache integration using 2025 best practices
  const { cacheExpandedMessages, hasExpandedCache, cachedMessages } =
    useModernMessageCache({ ticketId, enabled: !!tenantId });

  // Navigation detection: Reset expanded state when switching tickets
  const isNavigatingToNewTicket =
    currentTicketId !== null && currentTicketId !== ticketId;

  const [isExpanded, setIsExpanded] = useState(() => {
    // Reset expanded state on navigation, but preserve middle messages cache
    if (isNavigatingToNewTicket) {
      expandedStatesCache.delete(ticketId);
      return false;
    }
    return expandedStatesCache.get(ticketId) ?? false;
  });

  const [isLoadingCollapsed, setIsLoadingCollapsed] = useState(false);

  const hasInitializedRef = useRef(false);

  // Update current ticket tracking
  useEffect(() => {
    currentTicketId = ticketId;
  }, [ticketId]);

  // Reset state when ticket changes (navigation behavior)
  useEffect(() => {
    if (!hasInitializedRef.current) {
      hasInitializedRef.current = true;
      return;
    }

    // On navigation: reset expanded state
    setIsExpanded(false);
    expandedStatesCache.set(ticketId, false);
    setIsLoadingCollapsed(false);
  }, [ticketId]);

  // Update cache when state changes
  useEffect(() => {
    expandedStatesCache.set(ticketId, isExpanded);
  }, [ticketId, isExpanded]);

  // 2025 Modern Approach: Cache only on user actions, not in useEffect
  // Let useLiveQuery handle reactive updates automatically

  const shouldShowCollapsedIndicator = allMessages.length > 3 && !isExpanded;
  const collapsedCount = Math.max(0, allMessages.length - 2);

  const expandCollapsedMessages = useCallback(async () => {
    if (isExpanded || allMessages.length <= 3) return;

    // Check if we already have expanded cache for this ticket
    const isAlreadyCached = hasExpandedCache && cachedMessages.length > 0;

    // Only show loading if data is not cached
    if (!isAlreadyCached) {
      setIsLoadingCollapsed(true);
    }

    try {
      // Convert DisplayMessage to MessageData format for cache
      const messageData = allMessages.map((msg) => ({
        id: msg.id,
        content: msg.content,
        author_name: msg.author.name,
        author_avatar: msg.author.avatarUrl || '',
        created_at:
          typeof msg.createdAt === 'string'
            ? msg.createdAt
            : msg.createdAt?.toISOString() || new Date().toISOString(),
        attachments: msg.attachments || [],
      }));

      // Cache all messages when user expands (your simple logic)
      await cacheExpandedMessages(messageData);

      // Only add delay if data wasn't cached (for better UX on first load)
      if (!isAlreadyCached) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

      setIsExpanded(true);
      expandedStatesCache.set(ticketId, true);
    } catch (error) {
      console.error('Error loading collapsed messages:', error);
    } finally {
      setIsLoadingCollapsed(false);
    }
  }, [
    isExpanded,
    allMessages,
    ticketId,
    cacheExpandedMessages,
    hasExpandedCache,
    cachedMessages,
  ]);

  const displayMessages = (() => {
    if (isLoadingMessages || allMessages.length <= 3) {
      return allMessages;
    }

    if (!isExpanded) {
      // Show only first and last messages for performance optimization
      const first = allMessages[0];
      const last = allMessages[allMessages.length - 1];
      return first && last ? [first, last] : allMessages;
    }

    // Show all messages when expanded (from cache or newly loaded)
    return allMessages;
  })();

  return {
    displayMessages,
    collapsedCount,
    isExpanded,
    isLoadingCollapsed,
    shouldShowCollapsedIndicator,
    expandCollapsedMessages,
  };
}
