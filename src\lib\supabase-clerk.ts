'use client';

import { createClient } from '@supabase/supabase-js';
import { useSession, useUser } from '@clerk/nextjs';
import { useCallback, useMemo } from 'react';
import type { Database } from '@/types/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// CRITICAL FIX: Global singleton Supabase client to prevent multiple WebSocket connections
let globalSupabaseClient: ReturnType<typeof createClient<Database>> | null =
  null;

/**
 * Hook to create a Supabase client with Clerk authentication
 * Uses Clerk's native Supabase integration for secure token management
 * SINGLETON PATTERN: Ensures only one client instance exists globally
 */
export function useSupabaseClient() {
  const { session } = useSession();
  const { user } = useUser();

  const supabaseClient = useMemo(() => {
    // CRITICAL: Reuse existing client if available to prevent multiple WebSocket connections
    if (globalSupabaseClient) {
      console.log('♻️ Reusing existing global Supabase client');
      return globalSupabaseClient;
    }

    console.log('🔗 Creating new global Supabase client');
    globalSupabaseClient = createClient<Database>(
      supabaseUrl,
      supabaseAnonKey,
      {
        async accessToken() {
          try {
            // Authentication guard: ensure session exists and is valid
            if (!session || !session.status || session.status !== 'active') {
              return null;
            }
            return session?.getToken() ?? null;
          } catch (error) {
            // Gracefully handle session invalidation during logout
            console.warn('Session token access failed:', error);
            // Additional context for debugging
            if (session) {
              console.warn('Session status:', session.status);
            }
            return null;
          }
        },
        realtime: {
          // Enhanced real-time configuration for better stability
          params: {
            eventsPerSecond: 10, // Reduced from default to prevent overwhelming
          },
          heartbeatIntervalMs: 30000, // 30 seconds heartbeat
          reconnectAfterMs: (tries: number) => Math.min(tries * 1000, 10000), // Exponential backoff up to 10s
        },
      }
    );

    return globalSupabaseClient;
  }, []); // CRITICAL: Remove session dependency to prevent client recreation

  return {
    supabase: supabaseClient,
    user,
    isAuthenticated: !!user,
  };
}

/**
 * Hook for fetching tickets with proper authentication and tenant isolation
 */
export function useTickets(tenantId: string | null) {
  const { supabase, isAuthenticated } = useSupabaseClient();

  const fetchTickets = useCallback(async () => {
    if (!isAuthenticated || !tenantId) {
      return [];
    }

    const { data, error } = await supabase
      .from('tickets')
      .select('*')
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch tickets: ${error.message}`);
    }

    return data || [];
  }, [supabase, tenantId, isAuthenticated]);

  return { fetchTickets, isAuthenticated };
}

/**
 * Hook for updating tickets with proper authentication and tenant isolation
 */
export function useUpdateTicket() {
  const { supabase, user, isAuthenticated } = useSupabaseClient();

  const updateTicket = useCallback(
    async (
      ticketId: string,
      updates: {
        title?: string;
        description?: string;
        status?: string;
        priority?: string;
        department?: string;
      }
    ) => {
      if (!isAuthenticated || !user) {
        throw new Error('User must be authenticated to update tickets');
      }

      const { data, error } = await supabase
        .from('tickets')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', ticketId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update ticket: ${error.message}`);
      }

      return data;
    },
    [supabase, user, isAuthenticated]
  );

  return { updateTicket, isAuthenticated };
}

/**
 * Server-side Supabase client creation utility
 * For use in API routes and server components
 */
export function createServerSupabaseClient(authToken: string | null) {
  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    async accessToken() {
      return authToken;
    },
  });
}
