'use client';

import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { SafeHtml } from '@/features/shared/components/SafeHtml';
import { Badge } from '@/features/shared/components/ui/badge';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { ChevronDown, ChevronUp } from 'lucide-react';
import {
  useCallback,
  useLayoutEffect,
  useRef,
  useState,
  useEffect,
  memo,
  useMemo,
} from 'react';
import { Attachment, Ticket, TicketWithDetails } from '../models/ticket.schema';
import { AttachmentItem } from './AttachmentItem';

import AttachmentIndicator from './AttachmentIndicator';

type DisplayMessage = {
  id: string;
  author: {
    name: string;
    email?: string;
    avatarUrl?: string;
  };
  content: string;
  createdAt: Date;
  attachments?: Attachment[];
  isInternal?: boolean;
  isOptimistic?: boolean;
};

interface MessageItemProps {
  message: DisplayMessage;
  isInitiallyExpanded: boolean;
  isLastMessage: boolean;
  currentUserEmail?: string | undefined;
  ticket: Ticket;
  totalMessageCount: number;
}

// Helper function to determine message recipient display
function getMessageRecipientDisplay(
  message: DisplayMessage,
  ticket: Ticket,
  currentUserEmail?: string
): string {
  // Determine who the message is intended for based on ticket context
  let recipientName = '';
  let recipientEmail = '';

  // For admin/super_admin created tickets assigned to agents
  // CRITICAL FIX: Check for assigned tickets more robustly
  const isAdminCreated =
    ticket.metadata?.createdByUser?.role === 'admin' ||
    ticket.metadata?.createdByUser?.role === 'super_admin';

  // Also check if we have assignedUser data from the TicketWithDetails interface
  const assignedUser =
    ticket.metadata?.assignedUser || (ticket as TicketWithDetails).assignedUser;

  if (isAdminCreated && ticket.assignedTo && assignedUser) {
    // If message is from the ticket creator (admin), it goes to the assigned agent
    if (message.author.email === ticket.metadata.createdByUser.email) {
      recipientName = assignedUser.name;
      recipientEmail = assignedUser.email;
    }
    // If message is from the assigned agent, it goes back to the admin who created it
    else if (message.author.email === assignedUser.email) {
      recipientName = ticket.metadata.createdByUser.name;
      recipientEmail = ticket.metadata.createdByUser.email;
    }
  }
  // For user-created tickets OR when assignment info is not available
  else {
    // If message is from the ticket creator (user), determine recipient based on assignment
    if (message.author.email === ticket.userEmail) {
      // If ticket is assigned to a specific agent, show agent name
      if (ticket.assignedTo && assignedUser) {
        recipientName = assignedUser.name;
        recipientEmail = assignedUser.email;
      } else {
        // No specific assignment, show Support Team
        recipientName = 'Support Team';
        recipientEmail = 'support';
      }
    }
    // If message is from support/admin, it goes to the user
    else {
      recipientName = ticket.userName;
      recipientEmail = ticket.userEmail;
    }
  }

  // Display logic based on current user
  if (message.author.email === currentUserEmail) {
    // Current user is sending - show "to [recipient]"
    return `to ${recipientName}`;
  } else {
    // Someone else is sending - show "to me" if it's for current user, otherwise show recipient
    if (recipientEmail === currentUserEmail) {
      return 'to me';
    } else {
      return `to ${recipientName}`;
    }
  }
}

export const MessageItem = memo(function MessageItem({
  message,
  isInitiallyExpanded,
  isLastMessage,
  currentUserEmail,
  ticket,
  totalMessageCount,
}: MessageItemProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isExpanded, setIsExpanded] = useState(isInitiallyExpanded);
  const [isCollapsible, setIsCollapsible] = useState(false);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const canBeCollapsible = !isLastMessage;

  const analyzeContent = useCallback((html: string) => {
    if (typeof window === 'undefined' || !html) {
      return { isMultiBlock: false, text: '', sanitizedHtml: '' };
    }
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    const text = tempDiv.textContent || '';
    const blockElements = tempDiv.querySelectorAll(
      'p, h1, h2, h3, h4, h5, h6, ul, ol, li, blockquote, br'
    );
    const isMultiBlock = blockElements.length > 1;

    // Always preserve the original HTML formatting
    // Don't convert to plain text even for single blocks
    return { isMultiBlock, text, sanitizedHtml: html };
  }, []);

  const checkCollapsibility = useCallback(() => {
    if (!canBeCollapsible) {
      setIsCollapsible(false);
      setIsExpanded(true);
      return;
    }

    // NEW RULE: Only apply collapsible logic when there are 4+ total replies
    if (totalMessageCount < 4) {
      setIsCollapsible(false);
      setIsExpanded(true);
      return;
    }

    // Check for text overflow in container
    const element = contentRef.current;
    if (element) {
      const { isMultiBlock, text } = analyzeContent(message.content);

      // Multi-line messages are collapsible
      if (isMultiBlock) {
        setIsCollapsible(true);
        setIsExpanded(isInitiallyExpanded);
        return;
      }

      // Check if single-line text overflows container
      const tempElement = document.createElement('p');
      tempElement.style.position = 'absolute';
      tempElement.style.visibility = 'hidden';
      tempElement.style.whiteSpace = 'nowrap';
      tempElement.style.fontSize = '0.875rem';
      tempElement.style.fontFamily = getComputedStyle(element).fontFamily;
      tempElement.textContent = text;

      document.body.appendChild(tempElement);
      const textWidth = tempElement.offsetWidth;
      document.body.removeChild(tempElement);

      const containerWidth = element.offsetWidth;
      const isOverflowing = textWidth > containerWidth;

      // Messages with attachments are always collapsible (even if text doesn't overflow)
      const hasAttachments =
        message.attachments && message.attachments.length > 0;

      setIsCollapsible(!!isOverflowing || !!hasAttachments);
      setIsExpanded(
        isOverflowing || hasAttachments ? isInitiallyExpanded : true
      );
    }
  }, [
    canBeCollapsible,
    isInitiallyExpanded,
    analyzeContent,
    message.content,
    message.attachments,
    totalMessageCount,
  ]);

  useLayoutEffect(() => {
    checkCollapsibility();
  }, [checkCollapsibility]);

  // Debounced resize handler for responsive ellipsis
  useEffect(() => {
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      resizeTimeoutRef.current = setTimeout(() => {
        checkCollapsibility();
      }, 10); // 10ms debounce for smooth responsive behavior
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [checkCollapsibility]);

  // Memoize expensive computations
  const recipientDisplay = useMemo(() => {
    return getMessageRecipientDisplay(message, ticket, currentUserEmail);
  }, [message, ticket, currentUserEmail]);

  const formattedDate = useMemo(() => {
    try {
      const date =
        message.createdAt instanceof Date
          ? message.createdAt
          : new Date(message.createdAt);
      return format(date, "MMM d, yyyy 'at' h:mm a");
    } catch {
      return 'Invalid date';
    }
  }, [message.createdAt]);

  // Optimized toggle function with useCallback
  const toggleExpansion = useCallback(() => {
    if (isCollapsible) {
      setIsExpanded((prev) => !prev);
    }
  }, [isCollapsible]);

  const { text: plainText, sanitizedHtml } = analyzeContent(message.content);

  return (
    <div
      className={cn(
        'bg-white dark:bg-gray-800 transition-opacity duration-300',
        isCollapsible &&
          !isExpanded &&
          'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50',
        message.isOptimistic && 'opacity-70'
      )}
      onClick={isCollapsible && !isExpanded ? toggleExpansion : undefined}
    >
      <div className='flex gap-3 py-4 px-6 transition-colors'>
        <ProfileAvatar
          avatarUrl={message.author.avatarUrl ?? null}
          name={message.author.name}
          email={message.author.email ?? ''}
          className='h-10 w-10 mt-1'
        />
        <div className='flex-1 min-w-0'>
          <div className='flex items-start justify-between mb-1'>
            <div className='flex-1 min-w-0'>
              {/* Consistent single-line header format for both expanded and collapsed states */}
              <div className='flex items-center gap-2 text-sm'>
                <span className='font-medium text-gray-900 dark:text-gray-100 truncate'>
                  {message.author.name}
                </span>
                {message.author.email && (
                  <span className='text-gray-500 dark:text-gray-400 truncate'>
                    {recipientDisplay}
                  </span>
                )}
                <span className='text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap'>
                  {formattedDate}
                </span>
                {message.isInternal && (
                  <Badge
                    variant='secondary'
                    className='text-xs whitespace-nowrap'
                  >
                    Internal Note
                  </Badge>
                )}
              </div>
            </div>
            <div className='flex items-center gap-4 ml-auto shrink-0'>
              {/* Attachment indicator - only visible when collapsed and attachments exist */}
              {isCollapsible && !isExpanded && (
                <AttachmentIndicator count={message.attachments?.length || 0} />
              )}

              {isCollapsible && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleExpansion();
                  }}
                  className='flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 cursor-pointer'
                >
                  <span>{isExpanded ? 'Collapse' : 'Expand'}</span>
                  {isExpanded ? (
                    <ChevronUp className='h-3 w-3' />
                  ) : (
                    <ChevronDown className='h-3 w-3' />
                  )}
                </button>
              )}
            </div>
          </div>
          <div
            ref={contentRef}
            className={cn(
              'text-sm text-gray-600 dark:text-gray-400',
              isExpanded ? 'mt-2' : ''
            )}
          >
            {isExpanded ? (
              <SafeHtml
                content={sanitizedHtml}
                className='[&_p]:whitespace-pre-wrap'
              />
            ) : (
              <p className='truncate whitespace-nowrap overflow-hidden text-ellipsis'>
                {plainText}
              </p>
            )}
          </div>

          {isExpanded &&
            message.attachments &&
            message.attachments.length > 0 && (
              <div className='mt-4'>
                <h4 className='text-sm font-medium text-gray-900 dark:text-gray-100 mt-8 mb-3'>
                  {message.attachments.length} Attachments
                </h4>
                <div className='flex flex-wrap gap-3'>
                  {message.attachments.map((attachment) => (
                    <AttachmentItem
                      key={attachment.id}
                      attachment={attachment}
                    />
                  ))}
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
});
