import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

/**
 * Server-side Supabase client creation utility
 * For use in API routes and server components
 */
export function createServerSupabaseClient(authToken?: string | null) {
  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: authToken
        ? {
            Authorization: `Bearer ${authToken}`,
          }
        : {},
    },
  });
}

/**
 * Service role Supabase client for admin operations
 * Uses service role key for bypassing R<PERSON> when needed
 */
export function createServiceSupabaseClient() {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!serviceRoleKey) {
    throw new Error(
      'SUPABASE_SERVICE_ROLE_KEY environment variable is required'
    );
  }

  return createClient<Database>(supabaseUrl, serviceRole<PERSON><PERSON>, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}
