import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { z } from 'zod';

const searchParamsSchema = z.object({
  q: z.string().min(3, 'Search query must be at least 3 characters').optional(),
  role: z.union([z.string(), z.array(z.string())]).optional(),
  limit: z.coerce.number().min(1).max(50).default(10),
});

const VALID_ROLES = ['user', 'agent', 'admin', 'super_admin'];

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = Object.fromEntries(request.nextUrl.searchParams);
    const validation = searchParamsSchema.safeParse(searchParams);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid search parameters',
          details: validation.error.issues,
        },
        { status: 400 }
      );
    }

    const { q: query, role: roles, limit } = validation.data;

    const serviceClient = createServiceSupabaseClient();
    const { data: currentUser, error: userError } = await serviceClient
      .from('users')
      .select('tenant_id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    let usersQuery = serviceClient
      .from('users')
      .select(
        'id, email, first_name, last_name, role, status, avatar_url, clerk_id'
      )
      .eq('tenant_id', currentUser.tenant_id)
      .eq('status', 'active')
      .limit(limit);

    if (query) {
      const isUUID =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          query
        );
      usersQuery = isUUID
        ? usersQuery.eq('id', query)
        : usersQuery.or(
            `email.ilike.%${query}%,first_name.ilike.%${query}%,last_name.ilike.%${query}%`
          );
    }

    if (roles) {
      const rolesArray = Array.isArray(roles) ? roles : [roles];
      const invalidRoles = rolesArray.filter(
        (role) => !VALID_ROLES.includes(role)
      );
      if (invalidRoles.length > 0) {
        return NextResponse.json(
          { error: `Invalid roles: ${invalidRoles.join(', ')}` },
          { status: 400 }
        );
      }
      usersQuery = usersQuery.in('role', rolesArray);
    }

    const { data: users, error: queryError } = await usersQuery.order('email');

    if (queryError) {
      console.error('User search error:', queryError);
      return NextResponse.json(
        { error: 'Failed to search users' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      users: users.map((user) => ({
        id: user.id,
        email: user.email,
        name:
          [user.first_name, user.last_name].filter(Boolean).join(' ') ||
          user.email,
        role: user.role,
        status: user.status,
        avatar_url: user.avatar_url,
        clerk_id: user.clerk_id,
      })),
      total: users.length,
      query,
      roles: roles ? (Array.isArray(roles) ? roles : [roles]) : null,
    });
  } catch (error) {
    console.error('Unexpected error in user search:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
