'use client';

/**
 * Resource Preloader - React 19 Performance Optimized
 *
 * This utility provides React 19 resource preloading capabilities to optimize
 * critical asset loading and improve page performance.
 *
 * Key Features:
 * - React 19 resource preloading APIs
 * - DNS prefetching for external domains
 * - Script and stylesheet preloading
 * - Font preloading with proper CORS
 * - Image preloading for critical assets
 *
 * <AUTHOR> Augster
 * @version 1.0 - React 19 Optimized (January 2025)
 */

// React 19 resource preloading functions
declare global {
  function prefetchDNS(href: string): void;
  function preconnect(href: string, options?: { crossOrigin?: string }): void;
  function preload(
    href: string,
    options: { as: string; crossOrigin?: string; integrity?: string }
  ): void;
  function preinit(
    href: string,
    options: { as: string; crossOrigin?: string; integrity?: string }
  ): void;
}

/**
 * Preload critical DNS domains
 */
export function preloadCriticalDNS() {
  if (typeof prefetchDNS !== 'function') return;

  // Preload DNS for critical external domains
  const criticalDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://img.clerk.com',
    'https://images.clerk.dev',
  ];

  criticalDomains.forEach((domain) => {
    try {
      prefetchDNS(domain);
    } catch (error) {
      console.warn('Failed to prefetch DNS for:', domain, error);
    }
  });
}

/**
 * Preconnect to critical external services
 */
export function preconnectCriticalServices() {
  if (typeof preconnect !== 'function') return;

  const criticalServices = [
    { href: 'https://fonts.googleapis.com', crossOrigin: 'anonymous' },
    { href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' },
    { href: 'https://img.clerk.com', crossOrigin: 'anonymous' },
    { href: 'https://images.clerk.dev', crossOrigin: 'anonymous' },
  ];

  criticalServices.forEach(({ href, crossOrigin }) => {
    try {
      preconnect(href, { crossOrigin });
    } catch (error) {
      console.warn('Failed to preconnect to:', href, error);
    }
  });
}

/**
 * Preload critical fonts
 */
export function preloadCriticalFonts() {
  if (typeof preload !== 'function') return;

  const criticalFonts: string[] = [
    // Add your critical font URLs here
    // Example: '/fonts/inter-var.woff2'
  ];

  criticalFonts.forEach((fontUrl) => {
    try {
      preload(fontUrl, {
        as: 'font',
        crossOrigin: 'anonymous',
      });
    } catch (error) {
      console.warn('Failed to preload font:', fontUrl, error);
    }
  });
}

/**
 * Preload critical stylesheets
 */
export function preloadCriticalStyles() {
  if (typeof preinit !== 'function') return;

  const criticalStyles: string[] = [
    // Add your critical CSS URLs here
    // These will be loaded and applied immediately
  ];

  criticalStyles.forEach((styleUrl) => {
    try {
      preinit(styleUrl, {
        as: 'style',
      });
    } catch (error) {
      console.warn('Failed to preinit stylesheet:', styleUrl, error);
    }
  });
}

/**
 * Preload critical scripts
 */
export function preloadCriticalScripts() {
  if (typeof preinit !== 'function') return;

  const criticalScripts: string[] = [
    // Add your critical script URLs here
    // These will be loaded and executed immediately
  ];

  criticalScripts.forEach((scriptUrl) => {
    try {
      preinit(scriptUrl, {
        as: 'script',
      });
    } catch (error) {
      console.warn('Failed to preinit script:', scriptUrl, error);
    }
  });
}

/**
 * Preload critical images
 */
export function preloadCriticalImages() {
  if (typeof preload !== 'function') return;

  const criticalImages: string[] = [
    // Add your critical image URLs here
    // Example: '/images/logo.png'
  ];

  criticalImages.forEach((imageUrl) => {
    try {
      preload(imageUrl, {
        as: 'image',
      });
    } catch (error) {
      console.warn('Failed to preload image:', imageUrl, error);
    }
  });
}

/**
 * Initialize all critical resource preloading
 */
export function initializeCriticalResourcePreloading() {
  // Run preloading in the next tick to avoid blocking initial render
  setTimeout(() => {
    preloadCriticalDNS();
    preconnectCriticalServices();
    preloadCriticalFonts();
    preloadCriticalStyles();
    preloadCriticalScripts();
    preloadCriticalImages();
  }, 0);
}

/**
 * Preload route-specific resources
 */
export function preloadRouteResources(route: string) {
  if (typeof preload !== 'function') return;

  const routeResources: Record<string, string[]> = {
    '/tickets': [
      // Preload ticket-specific resources
    ],
    '/dashboard': [
      // Preload dashboard-specific resources
    ],
  };

  const resources = routeResources[route] || [];
  resources.forEach((resourceUrl) => {
    try {
      preload(resourceUrl, {
        as: 'fetch',
        crossOrigin: 'anonymous',
      });
    } catch (error) {
      console.warn('Failed to preload route resource:', resourceUrl, error);
    }
  });
}

/**
 * Hook for using resource preloading in React components
 */
export function useResourcePreloading(route?: string) {
  // Initialize critical resources on mount
  if (typeof window !== 'undefined') {
    initializeCriticalResourcePreloading();

    if (route) {
      preloadRouteResources(route);
    }
  }
}
