/**
 * Centralized Query Keys Factory - 2025 Optimized
 *
 * All query keys must include tenant_id as first element after the resource name
 * for proper tenant isolation and cache separation.
 */

export interface TicketFilters {
  status?: string[];
  priority?: string[];
  assignedTo?: string;
  createdBy?: string;
  roleFilter?: 'new' | 'assigned' | 'all';
}

export interface UserFilters {
  role?: string[];
  status?: string[];
  search?: string;
}

export const QueryKeys = {
  // Tickets
  TICKETS: {
    all: (tenantId: string) => ['tickets', tenantId] as const,
    list: (tenantId: string, filters?: TicketFilters) =>
      [...QueryKeys.TICKETS.all(tenantId), 'list', filters] as const,
    detail: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.all(tenantId), 'detail', ticketId] as const,
    messages: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.detail(tenantId, ticketId), 'messages'] as const,
    attachments: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.detail(tenantId, ticketId), 'attachments'] as const,
  },

  // Users
  USERS: {
    all: (tenantId: string) => ['users', tenantId] as const,
    list: (tenantId: string, filters?: UserFilters) =>
      [...QueryKeys.USERS.all(tenantId), 'list', filters] as const,
    detail: (tenantId: string, userId: string) =>
      [...QueryKeys.USERS.all(tenantId), 'detail', userId] as const,
    search: (tenantId: string, query: string) =>
      [...QueryKeys.USERS.all(tenantId), 'search', query] as const,
  },

  // Tenant-specific data
  TENANT: {
    all: (tenantId: string) => ['tenant', tenantId] as const,
    settings: (tenantId: string) =>
      [...QueryKeys.TENANT.all(tenantId), 'settings'] as const,
    stats: (tenantId: string) =>
      [...QueryKeys.TENANT.all(tenantId), 'stats'] as const,
  },

  // Real-time data (shorter cache times)
  REALTIME: {
    all: (tenantId: string) => ['realtime', tenantId] as const,
    notifications: (tenantId: string, userId: string) =>
      [...QueryKeys.REALTIME.all(tenantId), 'notifications', userId] as const,
    presence: (tenantId: string) =>
      [...QueryKeys.REALTIME.all(tenantId), 'presence'] as const,
  },
} as const;

/**
 * Helper function to invalidate all queries for a specific tenant
 * Useful for logout or tenant switching scenarios
 */
export const getTenantQueryPattern = (tenantId: string) => ({
  predicate: (query: { queryKey: unknown }) => {
    const queryKey = query.queryKey;
    return Array.isArray(queryKey) && queryKey.includes(tenantId);
  },
});

/**
 * Cache timing configurations by data type
 */
export const CACHE_CONFIG = {
  tickets: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },
  ticketMessages: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  },
  users: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },
  realtimeData: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
  tenantSettings: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  },
} as const;
