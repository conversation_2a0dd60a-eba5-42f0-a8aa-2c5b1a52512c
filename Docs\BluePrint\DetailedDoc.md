# DetailedDoc — Part 1: Architecture & Technology Stack with Tenant Isolation & Hybrid Sync (Updated for React Query v5)

---

## 1. Introduction to Architecture

The ticketing system is designed as a **Domain-Driven Modular Monolith** supporting **multi-tenancy with strict tenant data isolation** and enhanced by **React Query v5** for modern server-state management:

- **Monolithic app structure** organized into independent domains: `auth`, `ticketing`, `notifications`, `teams`, and `audit`.
- Tenant isolation implemented across UI, API, and database layers.
- React Query manages tenant-aware queries and mutations with built-in caching, persistence, and background synchronization.
- Zustand handles UI-local state and integrates seamlessly with React Query caches.

---

## 2. Tenant-Based Multi-Tenancy Overview

- Supports **true multi-tenancy** serving thousands of tenants simultaneously.
- Shared database schema with logical partitioning using `tenant_id`.
- Tenant isolation enforced by Supabase **Row Level Security (RLS)** and React Query query keys scoped by tenant.
- JWT tokens issued by <PERSON> include tenant context for all API calls and React Query fetchers.

---

## 3. Technology Stack Roles (Tenant-Aware)

| Technology                     | Role with Tenant Awareness                                                     |
| ------------------------------ | ------------------------------------------------------------------------------ |
| **Next.js 15.4.1**             | Frontend and backend server actions implement tenant-aware routing and APIs    |
| **React 19.1.0**               | UI rendering of tenant-scoped data reactively                                  |
| **React Query v5.83.0**        | Smarter data fetching with persistence, caching, background sync, and DevTools |
| **Clerk 6.25.0**               | Authentication and tenant-bound user identity management                       |
| **Supabase JS 2.51.0**         | Database access library enforcing tenant isolation and realtime subscriptions  |
| **Zustand 5.0.6**              | Tenant-scoped client state management for UI                                   |
| **Dexie.js 4.0.11**            | IndexedDB persistent client caching with React Query persister integration     |
| **Zod 4.0.5**                  | Tenant-aware input validation                                                  |
| **Tailwind CSS 4.x** + Plugins | Utility-first styling across tenants with forms/typography plugins             |
| **ShadCN UI**                  | Accessible, prebuilt UI components                                             |
| **Radix UI Components (v1.x)** | Advanced UI primitives like dialogs, dropdowns, and tooltips                   |
| **Lucide React 0.525.0**       | Icon library integrated with the UI                                            |
| **Sonner 2.0.6**               | Modern toast and notification system                                           |
| **React Hook Form 7.60.0**     | Form state management integrated with Zod validations                          |
| **Date-FNS 4.1.0**             | Tenant-aware date/time utilities for SLA and scheduling                        |

---

## 4. Updated Architecture Flow

```
┌─────────────────────────────────────────────────────────────────────────┐
│                            UI Layer (React 19)                         │
│  ┌───────────┐  ┌─────────────┐  ┌─────────────┐                       │
│  │ Tickets   │  │ Ticket View │  │ User Profile│                       │
│  └───────────┘  └─────────────┘  └─────────────┘                       │
└──────────────▲─────────────▲─────────────▲────────────────────────────┘
               │ Props/Events│ Props/Events│ Props/Events
┌──────────────▼─────────────▼─────────────▼────────────────────────────┐
│            Client State (Zustand) + Server State (React Query v5)      │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ Tenant ID, filters, modals (Zustand)                            │ │
│  │ Tenant-scoped queries & mutations (React Query)                 │ │
│  │ QueryOptions factory for consistent API calls                   │ │
│  │ Optimistic updates, background refetch, persistence             │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└──────────────▲─────────────▲─────────────▲────────────────────────────┘
               │ IndexedDB cache (Dexie.js + React Query persister)
┌──────────────▼─────────────▼─────────────▼────────────────────────────┐
│                        Server Layer (Next.js Server Actions)          │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ JWT validation, tenant scoping, business logic                  │ │
│  │ Supabase RLS policies enforce tenant isolation                  │ │
│  │ Real-time channels push updates to client cache                 │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────┘
```

---

## 5. Hybrid Client Sync Strategy

- **Local First:** React Query loads tenant-scoped data from Dexie.js instantly.
- **Realtime Updates:** Supabase subscriptions scoped by tenant push incremental data changes to React Query caches.
- **Server Fallback:** Missing or stale data is fetched asynchronously with background sync enabled.
- **Optimistic UI:** Client-side changes are reflected instantly in Zustand and React Query caches with rollback support.
- **Cache Management:** React Query `gcTime`, `staleTime`, and Dexie.js indexes optimize data freshness and performance.

---

## 6. Advantages of Updated Architecture

- Ultra-fast, responsive UI with React Query caching and Dexie.js persistence.
- Offline-first support using hybrid caching and background synchronization.
- Tenant isolation at every layer using JWT claims, query keys, and RLS policies.
- Scalable, modular design for future AI features and workflows.

# DetailedDoc — Part 2: Authentication & Tenant-Aware User Management (Updated for React Query v5)

---

## 1. Authentication with Clerk and React Query Integration

- Use **Clerk 6.25.0** for user authentication, including sign-up, sign-in, session management, and password reset.
- Clerk issues **JWT tokens** embedding critical claims such as `user_id`, `tenant_id`, and `role` to establish user identity and tenant context.
- React Query integrates with Clerk sessions by providing tenant-aware query context.
- JWT tokens are included in all API requests and React Query fetchers to enforce strict tenant-aware authorization.
- Clerk’s React hooks (e.g., `useUser()`) provide seamless access to current authenticated user info and claims in frontend components.

---

## 2. User-Tenant Association & Syncing

- On user login or signup, backend synchronizes Clerk user data with the Supabase `users` table, associating users with their `tenant_id` and role.
- React Query queries the `users` table with tenant-aware filtering using tenant IDs from JWT claims.
- Changes in user records are propagated to the React Query cache and Zustand session store for immediate frontend updates.

| Field                      | Description                             |
| -------------------------- | --------------------------------------- |
| `id`                       | UUID primary key                        |
| `tenant_id`                | UUID foreign key to tenant              |
| `clerk_id`                 | Clerk user identifier                   |
| `email`                    | User email                              |
| `role`                     | Tenant-scoped role (user, agent, admin) |
| `created_at`, `updated_at` | Timestamps                              |

---

## 3. Tenant-Aware JWT Tokens & Claims

- JWT tokens include the following structure for secure tenant-aware access:

```json
{
  "sub": "user-uuid",
  "tenant_id": "tenant-uuid",
  "role": "agent",
  "iat": 1723547800,
  "exp": 1723551400
}
```

- Backend APIs and Supabase RLS policies enforce tenant isolation based on these claims.
- React Query queries use these claims to scope data fetching, caching, and mutations per tenant.

---

## 4. Role-Based Access Control (RBAC)

- Define tenant-scoped roles to control user permissions:

| Role            | Permissions                                          |
| --------------- | ---------------------------------------------------- |
| **User**        | Create tickets, view and respond to own tickets only |
| **Agent**       | Manage tickets assigned within their tenant/team     |
| **Admin**       | Manage users, teams, and tickets within their tenant |
| **Super Admin** | (Optional) Full system-wide access across tenants    |

- RBAC is enforced at multiple layers:
  - Backend APIs and Supabase RLS.
  - React Query query keys and cache isolation per role.
  - Frontend conditional rendering of UI elements.

---

## 5. Frontend Session and State Management

- Use Clerk’s React hooks to access authenticated user metadata.
- Store `tenant_id`, `role`, and session info in **Zustand** global state.
- React Query queries and mutations consume tenant context to enforce strict isolation in cached data.
- On logout or tenant switch, React Query and Dexie.js caches are cleared to prevent cross-tenant leakage.

---

## 6. React Query v5 Integration for Authentication

- Integrate Clerk session context into React Query’s provider for consistent access across components.
- Use React Query’s `onError` hooks to gracefully handle authentication errors (e.g., token expiration).
- Cache invalidation policies ensure updated roles and permissions are reflected instantly.
- Dexie.js persistence ensures authentication context is maintained during offline usage and reconnects.

---

## 7. Security Considerations

- Verify JWT signatures and expiry on backend APIs.
- Reject any request where `tenant_id` in JWT does not match resource tenant.
- Use HTTPS and secure token storage in frontend apps.
- Configure React Query to refresh session tokens in the background for seamless UX.

# DetailedDoc — Part 3: Tenant-Aware Data Modeling & State Management (Updated for React Query v5)

---

## 1. Database Schema Design with Tenant Scope

- All tenant-specific tables include a mandatory **`tenant_id` UUID** field to enforce strict tenant data isolation.
- This tenant scoping is critical for Row Level Security (RLS) policies in Supabase and ensures consistent application logic.
- React Query queries and mutations use tenant IDs from JWT tokens to enforce scoped data access.

### Key Tables and Tenant Columns

| Table        | Important Columns                                                                                                   | Description                    |
| ------------ | ------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| `users`      | `id`, `tenant_id`, `clerk_id`, `email`, `role`, `created_at`, `updated_at`                                          | Tenant-associated users        |
| `tickets`    | `id`, `tenant_id`, `user_id`, `subject`, `description`, `priority`, `category`, `status`, `deadline`, `assigned_to` | Tenant-scoped tickets          |
| `responses`  | `id`, `tenant_id`, `ticket_id`, `user_id`, `message`, `created_at`                                                  | Tenant-scoped ticket responses |
| `audit_logs` | `id`, `tenant_id`, `ticket_id`, `user_id`, `action_type`, `old_value`, `new_value`, `timestamp`                     | Tenant-scoped audit trail      |

---

## 2. Input Validation Using Zod

- Use **Zod 4.0.5** schemas to validate all incoming API data and requests.
- Enforce presence and correctness of `tenant_id` and other critical fields to avoid cross-tenant data leaks.
- React Query uses these schemas for type-safe validation of server responses and mutations.

Example ticket creation schema:

```ts
import { z } from 'zod';

export const createTicketSchema = z.object({
  tenant_id: z.string().uuid(),
  user_id: z.string().uuid(),
  subject: z.string().min(5),
  description: z.string().min(10),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  category: z.string().optional(),
});
```

---

## 3. Frontend State Management with Zustand and React Query

- **Zustand 5.0.6** manages tenant-aware UI state slices such as filters, modals, and active tabs.
- **React Query v5.83.0** manages server state (queries and mutations) with tenant-specific caching and invalidation strategies.
- Use **query keys** that include `tenant_id` to isolate caches across tenants.
- Tenant-aware hooks combine Zustand and React Query for smooth, reactive UIs.

Example Zustand store setup:

```ts
import create from 'zustand';

const useStore = create((set) => ({
  tenant_id: null,
  tickets: [],
  responses: {},
  users: [],
  setTenant: (tenantId) => set({ tenant_id: tenantId }),
  setTickets: (tickets) => set({ tickets }),
  addResponse: (ticketId, response) =>
    set((state) => ({
      responses: {
        ...state.responses,
        [ticketId]: [...(state.responses[ticketId] || []), response],
      },
    })),
  setUsers: (users) => set({ users }),
}));
```

---

## 4. Dexie.js Persistent Cache with React Query Integration

- Dexie.js 4.0.11 is used for tenant-scoped caching in IndexedDB.
- React Query v5 integrates with Dexie.js via `createPersister` for offline support.
- Data loads instantly from Dexie on app startup and syncs in the background with server via React Query.
- Cache invalidation is triggered on logout or tenant switch to prevent cross-tenant data access.

---

## 5. Backend Data Access Patterns

- All backend API endpoints and server actions extract `tenant_id` from authenticated user JWT claims.
- React Query fetchers include `tenant_id` in every request for strict data scoping.
- Example SQL query filtering tickets by tenant:

```sql
SELECT * FROM tickets
WHERE tenant_id = current_setting('request.jwt.claim.tenant_id')::uuid;
```

---

## 6. Real-Time Updates with Supabase and React Query

- Supabase realtime channels push tenant-filtered data changes (insert/update/delete) to clients.
- React Query subscriptions update caches automatically.
- Zustand slices and Dexie caches are kept in sync via React Query’s `onSuccess` and `onSettled` hooks.

---

## 7. Soft Delete Strategy

- Soft deletes use status flags like `closed` instead of physical deletion.
- React Query refetches queries and purges closed tickets from active views.
- Closed tickets remain in IndexedDB and backend for auditing and potential restoration.

# DetailedDoc — Part 4: Ticket Lifecycle & Status Management with Tenant Isolation (Updated for React Query v5)

---

## 1. Ticket Status Definitions

Tickets progress through clearly defined statuses, scoped within each tenant context:

| Status        | Description                                              |
| ------------- | -------------------------------------------------------- |
| **New**       | Created and awaiting assignment within the tenant        |
| **Open**      | Actively being worked on by tenant’s support agents      |
| **Pending**   | Waiting on tenant user input or additional information   |
| **Resolved**  | Issue believed fixed, awaiting tenant user confirmation  |
| **Closed**    | Finalized and archived (soft deleted), no further action |
| **Escalated** | Requires urgent attention due to SLA breach or priority  |

---

## 2. Status Transitions & Business Rules

- Transitions are strictly tenant-scoped and validated using tenant context from JWT tokens.
- React Query manages server-state mutations for status updates and ensures that cache updates propagate instantly to the UI.
- Typical lifecycle transitions:
  - `New → Open`: Agent picks up the ticket.
  - `Open → Pending`: Waiting on user input.
  - `Pending → Open`: User responds.
  - `Open → Resolved`: Agent marks as resolved.
  - `Resolved → Closed`: User confirms or timeout occurs.
  - Any status → `Escalated`: Triggered by SLA violations or urgent issues.

Each transition triggers:

- React Query cache updates for tenant-specific queries.
- Supabase realtime events to sync changes across all clients.
- Tenant-scoped audit logs to track status changes.

---

## 3. Soft Delete via Closed Status

- Implement soft delete by setting ticket status to `Closed`.
- Closed tickets are filtered from active views by React Query selectors but remain in backend and IndexedDB for audit and potential restoration.
- React Query automatically refetches queries when status changes to maintain cache consistency.

---

## 4. Tenant-Aware Audit Logging

Every lifecycle event (status changes, escalations, soft deletes) is recorded with tenant context:

| Field         | Description                          |
| ------------- | ------------------------------------ |
| `tenant_id`   | Tenant owning the ticket             |
| `ticket_id`   | Ticket affected                      |
| `user_id`     | User performing the action           |
| `action_type` | e.g., `status_change`, `soft_delete` |
| `old_value`   | Previous status                      |
| `new_value`   | New status                           |
| `timestamp`   | Time of change                       |

---

## 5. SLA Enforcement & Escalations

- Backend jobs (e.g., Supabase Edge Functions) monitor SLA deadlines per tenant.
- Overdue tickets automatically escalate status and priority.
- React Query’s `refetchInterval` ensures UI reflects escalated statuses in near real-time.
- Notifications are sent to relevant tenant users, and all escalations are logged.

---

## 6. React Query v5 Integration in Status Management

- React Query mutations handle status updates and optimistic UI changes.
- Supabase realtime events synchronize status updates across all tenant users.
- Queries are invalidated and background refetched as needed to ensure data consistency.
- Use React Query’s `onMutate`, `onSuccess`, and `onSettled` hooks for smooth, tenant-aware lifecycle management.

# DetailedDoc — Part 5: Priority, Deadlines & Escalations with Tenant Isolation (Updated for React Query v5)

---

## 1. Priority Levels

Each ticket has a priority level scoped to its tenant, indicating urgency and influencing SLA enforcement:

| Priority   | Description                                     |
| ---------- | ----------------------------------------------- |
| **Low**    | Minor issues; longer resolution time acceptable |
| **Medium** | Standard priority with typical SLA timelines    |
| **High**   | Requires quicker response and handling          |
| **Urgent** | Critical issues needing immediate attention     |

---

## 2. Setting Priorities

- Users select priority upon ticket creation.
- Tenant agents and admins can override priority to reflect actual urgency.
- A default priority (typically Medium) is assigned if none is specified.
- React Query uses priority-aware query keys and caching strategies to ensure UI updates accurately reflect priority levels.

---

## 3. Deadlines and Service Level Agreements (SLAs)

- Each priority level corresponds to a tenant-scoped SLA deadline:

| Priority | SLA Deadline |
| -------- | ------------ |
| Urgent   | 4 hours      |
| High     | 1 day        |
| Medium   | 3 days       |
| Low      | 7 days       |

- Deadlines are set on ticket creation or when priority changes.
- Backend jobs (e.g., Supabase Edge Functions) monitor these deadlines.

---

## 4. Automatic Priority Escalation

- Tenant-aware backend jobs scan tickets against SLA deadlines.
- Overdue tickets automatically escalate to a higher priority within their tenant scope.
- React Query’s `refetchInterval` ensures escalations are reflected in the UI without requiring manual refresh.
- Notifications are sent to the tenant’s support team members, and all escalations are logged.

---

## 5. Manual Priority Management

- Tenant agents and admins can manually update ticket priorities via UI or API.
- React Query mutations apply changes optimistically, update the local cache, and trigger background syncing with the backend.
- Supabase realtime subscriptions propagate these updates to all active clients.

---

## 6. React Query v5 Integration in Priority Management

- React Query hooks (`useMutation`, `useQuery`) handle priority changes and SLA deadline updates.
- Use `queryOptions` with tenant and priority context to optimize caching and refetching.
- Dexie.js persistence ensures priority changes are cached locally and sync correctly in offline scenarios.
- Supabase realtime updates ensure all client caches stay in sync across tenant users.

# DetailedDoc — Part 6: Roles, Teams, Auditing & Monitoring with Tenant Isolation (Updated for React Query v5)

---

## 1. User Roles and Permissions

- Define tenant-scoped roles governing user capabilities within their tenant:

| Role              | Permissions                                                    |
| ----------------- | -------------------------------------------------------------- |
| **User**          | Create tickets, view/respond to own tickets only               |
| **Support Agent** | Manage tickets assigned to their tenant/team                   |
| **Admin**         | Manage users, teams, tickets, and settings within their tenant |
| **Super Admin**   | (Optional) Full system-wide access across tenants              |

- Roles are stored in the `users` table with `tenant_id` for tenant isolation.
- React Query queries and mutations are scoped using role-aware query keys to prevent unauthorized data access.
- Supabase RLS enforces access control based on roles.

---

## 2. Team Management

- Support agents are grouped into tenant-scoped teams or departments.
- Tickets have an `assigned_to` field linked to users or teams within the tenant.
- Tenant admins can assign or reassign tickets via React Query mutations for immediate UI updates.

---

## 3. Audit Logging

- Audit logs track critical actions including:
  - Ticket status changes
  - Priority updates
  - User and team management events
  - Ticket assignments

| Field         | Description                         |
| ------------- | ----------------------------------- |
| `tenant_id`   | Tenant owning the action            |
| `ticket_id`   | Ticket affected (if applicable)     |
| `user_id`     | User performing the action          |
| `action_type` | e.g., `status_change`, `assignment` |
| `old_value`   | Previous value                      |
| `new_value`   | New value                           |
| `timestamp`   | Time of change                      |

- React Query’s `onSuccess` hooks update cached audit logs after successful mutations.
- Supabase realtime channels push audit log changes to all connected clients.

---

## 4. Monitoring and Dashboards

- Tenant admins and super admins view dashboards showing:
  - Ticket volumes by status and priority
  - SLA compliance and escalation trends
  - Audit log summaries filtered by tenant

- React Query queries fetch dashboard data with caching and background refetching for real-time insights.

---

## 5. Approval Workflows (Optional)

- Implement approval workflows for sensitive actions (e.g., priority escalations):
  - Agents submit change requests.
  - Tenant admins approve/reject them.
  - React Query ensures state sync across all clients.

---

## 6. React Query v5 Integration for Roles and Monitoring

- Role-based API endpoints are consumed with React Query `queryOptions` to enforce access control.
- React Query DevTools assist in monitoring tenant-scoped queries and cache state.
- Supabase realtime updates propagate role and team changes instantly to all connected clients.

# DetailedDoc — Part 7: Final Wrap-up and Best Practices (Updated for React Query v5)

---

## 1. Summary of Tenant-Aware Architecture

- The system is a **Domain-Driven Modular Monolith** that supports **true multi-tenancy** with strict data isolation.
- Tenant isolation is enforced across all layers: frontend (React 19), backend (Next.js 15.4.1), database (Supabase RLS), and authentication (Clerk 6.25.0).
- React Query v5 manages server state with advanced caching, background synchronization, and offline persistence.
- Zustand complements React Query by managing UI-local state and session metadata.
- Hybrid sync with Dexie.js enables instant UI responsiveness and offline-first capability.
- Role-Based Access Control (RBAC) and team management ensure secure and granular permission enforcement.
- Audit logging and monitoring dashboards provide visibility and accountability per tenant.

---

## 2. Best Practices for React Query v5 Integration

### Tenant Isolation

- Always include `tenant_id` in query keys and mutation contexts to scope data and cache.
- Enforce tenant filtering with Supabase RLS policies and validate JWT tokens for every API call.

### React Query Patterns

- Use **QueryOptions Factory** for consistent, type-safe API configurations across components.
- Prefer `useQuery`, `useMutation`, and `useInfiniteQuery` for declarative data fetching and pagination.
- Configure `staleTime` and `gcTime` for an optimal balance between data freshness and performance.
- Leverage React Query DevTools for debugging cache and background sync behavior.

### State Management

- Use React Query for server state and Zustand for UI state (filters, modals, session info).
- Keep Zustand slices and React Query caches synchronized during tenant switches.

### Offline Support and Caching

- Integrate Dexie.js with React Query’s `createPersister` for offline persistence.
- Preload tenant-scoped data from IndexedDB for instant UI rendering and synchronize in the background.

### Security Considerations

- Verify JWT tokens and role claims in all backend APIs.
- React Query’s `onError` handlers gracefully manage token expiration and refresh.
- Clear caches securely on logout or tenant changes to avoid cross-tenant data exposure.

---

## 3. Operational Recommendations

- Monitor tenant usage, SLA compliance, and escalation trends using dashboards.
- Implement alerts for critical system health and tenant-specific metrics.
- Automate RBAC and tenant scoping tests to ensure consistent isolation.
- Plan for future scale by modularizing domains and considering microservices when necessary.

---

## 4. Future Enhancements

- Support tenant-specific feature flags and configurations.
- Add advanced approval workflows for high-privilege actions.
- Introduce AI-driven automation and proactive notifications.
- Extend audit logging to capture more granular user actions.

---

## 5. Developer Collaboration Practices

- Maintain clean, minimal, and maintainable code following SOLID, DRY, KISS, and YAGNI principles.
- Use React Query patterns consistently across teams for unified caching and data-fetching behavior.
- Automate tenant-aware integration tests to prevent regressions in data isolation and security.

# SimpleDoc — Part 1: System Architecture & Project Setup with Tenant Isolation (Updated for React Query v5)

---

## 1. Architecture Overview

- The app is a **modular monolith**: one deployable unit with clear modules like auth, ticketing, teams, and notifications.
- Designed for **multi-tenancy**: many tenants share the same system securely.
- Tenant data is isolated using a **shared database schema** with a `tenant_id` field in every relevant table.
- Tenant isolation is enforced on the frontend, backend, and database layers.
- **React Query v5** handles server-state with caching, persistence, background sync, and offline support.
- Zustand manages UI-local state and integrates seamlessly with React Query.

---

## 2. Multi-Tenancy & Tenant Isolation

- Each tenant corresponds to an organization or client.
- Data from different tenants is separated logically by `tenant_id`.
- Supabase Row Level Security (RLS) enforces tenant isolation at the database level using JWT claims.
- JWT tokens from Clerk embed tenant ID and user roles.
- React Query queries and mutations include tenant context in their keys for scoped caching.

---

## 3. Updated Technology Stack

| Technology                     | Role in Tenant-Aware Architecture                             |
| ------------------------------ | ------------------------------------------------------------- |
| **Next.js 15.4.1**             | Frontend and backend with tenant-scoped APIs                  |
| **React 19.1.0**               | UI rendering tenant-specific data                             |
| **React Query v5.83.0**        | Smarter data fetching, caching, persistence, and DevTools     |
| **Clerk 6.25.0**               | Auth and tenant-aware JWT tokens                              |
| **Supabase JS 2.51.0**         | DB with RLS and realtime tenant-filtered updates              |
| **Zustand 5.0.6**              | Tenant-scoped frontend state management                       |
| **Dexie.js 4.0.11**            | Client cache with React Query persistence for offline support |
| **Zod 4.0.5**                  | Input validation including tenant IDs                         |
| **Tailwind CSS 4.x** + Plugins | Consistent UI styling across tenants                          |
| **Radix UI Components (v1.x)** | Advanced UI primitives for dialogs, dropdowns, and modals     |
| **Lucide React 0.525.0**       | Icon library for clean UI                                     |
| **Sonner 2.0.6**               | Toast notifications and alerts                                |

---

## 4. Data Flow with Tenant Awareness

- Frontend stores tenant info after login using Clerk and Zustand.
- All API requests send JWT with tenant claims.
- Backend extracts `tenant_id` and enforces it in queries.
- React Query manages cached server-state scoped by tenant.
- Supabase realtime subscriptions push updates filtered for the current tenant.

---

## 5. Hybrid Client Sync Strategy

- **Local First:** React Query fetches from Dexie.js for instant UI rendering.
- **Realtime Updates:** Supabase subscriptions update React Query caches for live collaboration.
- **Server Fallback:** React Query background refetch ensures stale data is refreshed as needed.
- **Optimistic UI:** Changes reflect immediately in Zustand and React Query with backend sync.

---

## 6. Advantages of the Updated Setup

- **Fast and Responsive UI** with React Query caching and Dexie.js persistence.
- **Offline-first Support** for uninterrupted user experience.
- **Secure Multi-Tenancy** with strict isolation enforced at all layers.
- **Scalable and Maintainable** architecture for future AI features and workflows.
