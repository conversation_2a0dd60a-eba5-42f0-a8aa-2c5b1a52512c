/**
 * Role-based filtering types for ticket assignment and visibility system
 * @version 1.0 - React 19 Enterprise Implementation (January 2025)
 */

import { Ticket } from '../models/ticket.schema';

/**
 * User roles in the system with hierarchical permissions
 */
export type UserRole = 'super_admin' | 'admin' | 'agent' | 'user';

/**
 * Role-based filtering context for ticket queries
 */
export interface RoleBasedFilterContext {
  userId: string;
  role: UserRole;
  tenantId: string;
  email: string;
}

/**
 * Ticket filtering parameters for role-based access
 */
export interface TicketFilterParams {
  role: UserRole;
  userId: string;
  tenantId: string;
  status?: string[];
  assignedOnly?: boolean;
  createdOnly?: boolean;
}

/**
 * Assignment metadata for tracking ticket assignments
 */
export interface TicketAssignmentMetadata {
  assignedTo?: string;
  assignedBy?: string;
  assignedAt?: Date;
  assignedToUser?: {
    id: string;
    name: string;
    email: string;
    role: UserRole;
  };
  assignedByUser?: {
    id: string;
    name: string;
    email: string;
    role: UserRole;
  };
}

/**
 * Enhanced ticket with assignment metadata
 */
export interface TicketWithAssignment extends Ticket {
  assignmentMetadata?: TicketAssignmentMetadata;
}

/**
 * Role-based ticket visibility rules
 */
export interface TicketVisibilityRules {
  canView: boolean;
  canEdit: boolean;
  canAssign: boolean;
  canReply: boolean;
  canClose: boolean;
  reason?: string;
}

/**
 * Ticket filtering result with metadata
 */
export interface FilteredTicketsResult {
  tickets: Ticket[];
  totalCount: number;
  filteredCount: number;
  filterContext: RoleBasedFilterContext;
  appliedFilters: TicketFilterParams;
}

/**
 * Role-based section configuration for dashboard
 */
export interface RoleBasedSection {
  key: string;
  title: string;
  description: string;
  filterFn: (tickets: Ticket[], context: RoleBasedFilterContext) => Ticket[];
  visible: boolean;
}

/**
 * Dashboard configuration based on user role
 */
export interface RoleDashboardConfig {
  role: UserRole;
  sections: RoleBasedSection[];
  defaultSection: string;
  permissions: {
    canCreateTickets: boolean;
    canAssignTickets: boolean;
    canViewAllTickets: boolean;
    canManageUsers: boolean;
  };
}

/**
 * Assignment operation request
 */
export interface AssignTicketRequest {
  ticketId: string;
  assignedTo: string;
  assignedBy: string;
  tenantId: string;
  reason?: string;
}

/**
 * Assignment operation response
 */
export interface AssignTicketResponse {
  success: boolean;
  ticket: Ticket;
  assignment: TicketAssignmentMetadata;
  message: string;
}

/**
 * Role-based filtering utility function type
 */
export type RoleBasedFilterFunction = (
  tickets: Ticket[],
  context: RoleBasedFilterContext
) => Ticket[];

/**
 * Ticket ownership determination
 */
export interface TicketOwnership {
  isCreator: boolean;
  isAssigned: boolean;
  isAssigner: boolean;
  canAccess: boolean;
  accessReason:
    | 'creator'
    | 'assigned'
    | 'assigner'
    | 'admin'
    | 'super_admin'
    | 'none';
}
