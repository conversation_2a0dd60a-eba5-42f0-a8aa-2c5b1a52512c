// eslint.config.mjs

import js from '@eslint/js';
import pluginNext from '@next/eslint-plugin-next';
import reactHooks from 'eslint-plugin-react-hooks';
import prettier from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier/flat';
import tseslint from 'typescript-eslint';

export default [
  // 🌟 Base ESLint recommended rules
  js.configs.recommended,

  // 🌟 TypeScript configuration
  ...tseslint.configs.recommended,

  // 🌟 Next.js + React Hooks + Prettier integration
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        // ✅ Add Node.js globals for config files (process, module, etc.)
        process: 'readonly',
        __dirname: 'readonly',
        module: 'readonly',
        require: 'readonly',
      },
    },
    plugins: {
      '@next/next': pluginNext,
      'react-hooks': reactHooks,
      prettier, // 👈 Prettier as ESLint plugin
    },
    rules: {
      // ✅ Next.js recommended rules
      ...pluginNext.configs.recommended.rules,
      ...pluginNext.configs['core-web-vitals'].rules,

      // ✅ React Hooks rules
      ...reactHooks.configs.recommended.rules,

      // ✅ Prettier integration: Treat Prettier issues as ESLint errors
      'prettier/prettier': 'error',
    },
  },

  // 🌟 Prettier config: disable ESLint rules that conflict with Prettier
  prettierConfig,

  // 🌟 Custom rules
  {
    rules: {
      '@typescript-eslint/no-unused-vars': 'warn',
    },
  },

  // 🌟 Node.js environment for all config files (*.mjs, *.cjs, *.config.*)
  {
    files: [
      '**/*.config.{js,cjs,mjs}',
      '**/*.config.{ts,cts,mts}',
      '**/*.rc.{js,cjs,mjs}',
    ],
    languageOptions: {
      globals: {
        process: 'readonly',
        module: 'readonly',
        require: 'readonly',
        __dirname: 'readonly',
      },
    },
  },
];
