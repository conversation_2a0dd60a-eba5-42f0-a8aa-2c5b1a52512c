'use client';

import { memo } from 'react';
import { ProfileAvatar } from './ProfileAvatar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/features/shared/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface Agent {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
  clerk_id?: string;
}

interface AgentAssignment {
  agent: Agent;
  departments: string[];
}

interface AvatarGroupProps {
  /** Array of agent assignments with their departments */
  agentAssignments: AgentAssignment[];
  /** Maximum number of avatars to show before showing +N indicator */
  maxVisible?: number;
  /** Size of the avatars */
  size?: 'sm' | 'md' | 'lg';
  /** Additional className for the container */
  className?: string;
  /** Function to format department names for display */
  formatDepartments?: (departments: string[]) => string;
}

const sizeConfig = {
  sm: {
    avatar: 'h-8 w-8',
    overlap: '-ml-2',
    text: 'text-xs',
    counter: 'h-8 w-8 text-xs',
  },
  md: {
    avatar: 'h-10 w-10',
    overlap: '-ml-3',
    text: 'text-sm',
    counter: 'h-10 w-10 text-sm',
  },
  lg: {
    avatar: 'h-12 w-12',
    overlap: '-ml-4',
    text: 'text-base',
    counter: 'h-12 w-12 text-base',
  },
};

/**
 * AvatarGroup Component
 *
 * Displays multiple user avatars in an overlapping group with tooltips.
 * Supports showing a limited number of avatars with a "+N more" indicator.
 *
 * Features:
 * - Overlapping avatar display with proper z-index stacking
 * - Responsive sizing (sm, md, lg)
 * - Tooltips showing agent name and assigned departments
 * - "+N more" indicator for overflow
 * - Optimized for performance with memo
 *
 * <AUTHOR> Augster
 * @version 1.0 - Avatar Group Component (January 2025)
 */
export const AvatarGroup = memo<AvatarGroupProps>(
  ({
    agentAssignments,
    maxVisible = 4,
    size = 'md',
    className,
    formatDepartments = (departments) => departments.join(', '),
  }) => {
    const config = sizeConfig[size];
    const visibleAssignments = agentAssignments.slice(0, maxVisible);
    const remainingCount = Math.max(0, agentAssignments.length - maxVisible);

    if (agentAssignments.length === 0) {
      return null;
    }

    return (
      <div className={cn('flex items-center', className)}>
        {visibleAssignments.map((assignment, index) => (
          <Tooltip key={assignment.agent.id}>
            <TooltipTrigger asChild>
              <div
                className={cn(
                  'relative border-2 border-white dark:border-gray-800 rounded-full',
                  index > 0 && config.overlap
                )}
                style={{ zIndex: visibleAssignments.length - index }}
              >
                <ProfileAvatar
                  avatarUrl={assignment.agent.avatar_url || null}
                  name={assignment.agent.name}
                  email={assignment.agent.email}
                  clerkId={assignment.agent.clerk_id || ''}
                  className={config.avatar}
                  fallbackClassName='bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 font-medium'
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className='text-center'>
                <div className='font-medium'>{assignment.agent.name}</div>
                <div className={cn('opacity-80', config.text)}>
                  {formatDepartments(assignment.departments)}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        ))}

        {remainingCount > 0 && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className={cn(
                  'relative border-2 border-white dark:border-gray-800 rounded-full',
                  'bg-gray-100 dark:bg-gray-700 flex items-center justify-center',
                  'text-gray-600 dark:text-gray-300 font-medium',
                  config.counter,
                  config.overlap
                )}
                style={{ zIndex: 0 }}
              >
                +{remainingCount}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className='text-center'>
                <div className='font-medium'>
                  {remainingCount} more agent{remainingCount === 1 ? '' : 's'}
                </div>
                <div className={cn('opacity-80', config.text)}>
                  {agentAssignments
                    .slice(maxVisible)
                    .map((a) => a.agent.name)
                    .join(', ')}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    );
  }
);

AvatarGroup.displayName = 'AvatarGroup';
