'use client';

/**
 * Dynamic RichTextEditor - Performance Optimized with Code Splitting
 *
 * This component provides a dynamically imported RichTextEditor to reduce
 * initial bundle size and improve page load performance.
 *
 * Key Features:
 * - Dynamic import with loading state
 * - SSR disabled for better performance
 * - Proper TypeScript support
 * - Fallback loading component
 *
 * <AUTHOR> Augster
 * @version 1.0 - Dynamic Import Optimized (January 2025)
 */

import dynamic from 'next/dynamic';
import { ComponentProps } from 'react';
import { RichTextEditor } from './RichTextEditor';

// Loading component for the rich text editor
const RichTextEditorLoading = () => (
  <div className='relative'>
    <div className='file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground border-input block w-full min-w-0 rounded-md border px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none md:text-sm min-h-[180px] animate-pulse bg-gray-100 dark:bg-gray-700'></div>
  </div>
);

// Dynamic import of the RichTextEditor
const DynamicRichTextEditor = dynamic(
  () =>
    import('./RichTextEditor').then((mod) => ({ default: mod.RichTextEditor })),
  {
    loading: RichTextEditorLoading,
    ssr: false, // Disable SSR for better performance
  }
);

// Export with proper TypeScript support
export type DynamicRichTextEditorProps = ComponentProps<typeof RichTextEditor>;

export { DynamicRichTextEditor };
export default DynamicRichTextEditor;
