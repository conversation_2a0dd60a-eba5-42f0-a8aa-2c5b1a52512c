'use client';

import { ProfileSection } from './sections/ProfileSection';
import { AppearanceSection } from './sections/AppearanceSection';
import { SecuritySection } from './sections/SecuritySection';
import { NotificationsSection } from './sections/NotificationsSection';
import { AdminSection } from './sections/AdminSection';
import { DepartmentSection } from './sections/DepartmentSection';
import { AssignmentSection } from './sections/AssignmentSection';
import { SLASection } from './sections/SLASection';
import { AutoCloseSection } from './sections/AutoCloseSection';

interface SettingsContentProps {
  activeSection: string;
  hasAdminAccess: boolean;
}

export function SettingsContent({
  activeSection,
  hasAdminAccess,
}: SettingsContentProps) {
  const renderSection = () => {
    switch (activeSection) {
      case 'profile':
        return <ProfileSection />;
      case 'appearance':
        return <AppearanceSection />;
      case 'security':
        return <SecuritySection />;
      case 'notifications':
        return <NotificationsSection />;
      case 'admin':
        return hasAdminAccess ? <AdminSection /> : null;
      case 'departments':
        return hasAdminAccess ? <DepartmentSection /> : null;
      case 'assignment':
        return hasAdminAccess ? <AssignmentSection /> : null;
      case 'sla':
        return hasAdminAccess ? <SLASection /> : null;
      case 'auto-close':
        return hasAdminAccess ? <AutoCloseSection /> : null;
      default:
        return <ProfileSection />;
    }
  };

  return <div className='space-y-6'>{renderSection()}</div>;
}
