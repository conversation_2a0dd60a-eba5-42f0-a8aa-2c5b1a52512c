import { useCallback } from 'react';
import { countWords } from '@/lib/utils';

interface ReplyDraft {
  content: string;
  lastModified: number;
}

const DRAFT_STORAGE_PREFIX = 'reply-draft-';
const MIN_WORDS_FOR_DRAFT = 5;

/**
 * Hook for managing reply draft persistence per ticket
 * Provides methods to save, load, and clear drafts for specific tickets
 */
export function useReplyDraftPersistence() {
  // Get storage key for a specific ticket
  const getDraftKey = useCallback((ticketId: string) => {
    return `${DRAFT_STORAGE_PREFIX}${ticketId}`;
  }, []);

  // Save draft for a specific ticket (with SSR safety)
  const saveDraft = useCallback(
    (ticketId: string, content: string) => {
      try {
        if (typeof window !== 'undefined' && window.localStorage) {
          const wordCount = countWords(content);
          if (wordCount >= MIN_WORDS_FOR_DRAFT) {
            const draft: ReplyDraft = {
              content,
              lastModified: Date.now(),
            };
            localStorage.setItem(getDraftKey(ticketId), JSON.stringify(draft));
          } else {
            // Clear draft if content is too short
            localStorage.removeItem(getDraftKey(ticketId));
          }
        }
      } catch {
        // Silently fail if localStorage is not available
      }
    },
    [getDraftKey]
  );

  // Load draft for a specific ticket (with SSR safety)
  const loadDraft = useCallback(
    (ticketId: string): string | null => {
      try {
        if (typeof window !== 'undefined' && window.localStorage) {
          const draftData = localStorage.getItem(getDraftKey(ticketId));
          if (draftData) {
            const draft: ReplyDraft = JSON.parse(draftData);
            return draft.content;
          }
        }
      } catch {
        // Silently fail if localStorage is not available or data is corrupted
      }
      return null;
    },
    [getDraftKey]
  );

  // Clear draft for a specific ticket (with SSR safety)
  const clearDraft = useCallback(
    (ticketId: string) => {
      try {
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.removeItem(getDraftKey(ticketId));
        }
      } catch {
        // Silently fail if localStorage is not available
      }
    },
    [getDraftKey]
  );

  // Check if draft exists for a specific ticket
  const hasDraft = useCallback(
    (ticketId: string): boolean => {
      const draft = loadDraft(ticketId);
      return draft !== null && countWords(draft) >= MIN_WORDS_FOR_DRAFT;
    },
    [loadDraft]
  );

  // Check if content has enough words to warrant draft confirmation
  const hasValidContent = useCallback((content: string): boolean => {
    return countWords(content) >= MIN_WORDS_FOR_DRAFT;
  }, []);

  return {
    saveDraft,
    loadDraft,
    clearDraft,
    hasDraft,
    hasValidContent,
    MIN_WORDS_FOR_DRAFT,
  };
}
