'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/features/shared/components/ui/button';
import { Input } from '@/features/shared/components/ui/input';
import { Switch } from '@/features/shared/components/ui/switch';
import { Label } from '@/features/shared/components/ui/label';
import { toast } from '@/features/shared/components/toast';
import { useAuth } from '@/features/shared/hooks/useAuth';

interface SLAConfigurationData {
  urgent_deadline_hours: number;
  high_deadline_hours: number;
  medium_deadline_hours: number;
  low_deadline_hours: number;
  escalation_enabled: boolean;
}

// API functions
async function fetchSLAConfiguration() {
  const response = await fetch('/api/settings/sla');
  if (!response.ok) {
    throw new Error('Failed to fetch SLA configuration');
  }
  const result = await response.json();
  return result.data;
}

async function updateSLAConfiguration(data: SLAConfigurationData) {
  const response = await fetch('/api/settings/sla', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update SLA configuration');
  }

  return response.json();
}

async function resetSLAConfiguration() {
  const response = await fetch('/api/settings/sla', {
    method: 'DELETE',
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to reset SLA configuration');
  }

  return response.json();
}

export function SLAConfigurationForm() {
  const { tenantId } = useAuth();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState<SLAConfigurationData>({
    urgent_deadline_hours: 4,
    high_deadline_hours: 24,
    medium_deadline_hours: 72,
    low_deadline_hours: 168,
    escalation_enabled: true,
  });

  // Fetch SLA configuration
  const { data: slaConfig, isLoading } = useQuery({
    queryKey: ['sla-configuration', tenantId],
    queryFn: fetchSLAConfiguration,
    enabled: !!tenantId,
  });

  // Update form data when config loads
  useEffect(() => {
    if (slaConfig) {
      setFormData({
        urgent_deadline_hours: slaConfig.urgent_deadline_hours,
        high_deadline_hours: slaConfig.high_deadline_hours,
        medium_deadline_hours: slaConfig.medium_deadline_hours,
        low_deadline_hours: slaConfig.low_deadline_hours,
        escalation_enabled: slaConfig.escalation_enabled,
      });
    }
  }, [slaConfig]);

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: updateSLAConfiguration,
    onSuccess: () => {
      const { escalation_enabled } = formData;

      if (escalation_enabled) {
        toast.success('SLA management enabled successfully', {
          description:
            'Tickets will now be escalated based on your configured rules',
        });
      } else {
        toast.success('SLA management disabled successfully', {
          description: 'Automatic ticket escalation is now turned off',
        });
      }

      queryClient.invalidateQueries({ queryKey: ['sla-configuration'] });
    },
    onError: (error: Error) => {
      toast.error('Error', {
        description: error.message,
      });
    },
  });

  // Reset mutation
  const resetMutation = useMutation({
    mutationFn: resetSLAConfiguration,
    onSuccess: () => {
      toast.success('SLA settings reset successfully', {
        description:
          'SLA management enabled with default escalation rules restored',
      });
      queryClient.invalidateQueries({ queryKey: ['sla-configuration'] });
    },
    onError: (error: Error) => {
      toast.error('Error', {
        description: error.message,
      });
    },
  });

  const handleInputChange = (
    field: keyof SLAConfigurationData,
    value: number | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const {
      urgent_deadline_hours,
      high_deadline_hours,
      medium_deadline_hours,
      low_deadline_hours,
    } = formData;

    if (
      urgent_deadline_hours >= high_deadline_hours ||
      high_deadline_hours >= medium_deadline_hours ||
      medium_deadline_hours >= low_deadline_hours
    ) {
      toast.error('Validation Error', {
        description:
          'Deadline hours must follow progression: urgent < high < medium < low',
      });
      return false;
    }

    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      updateMutation.mutate(formData);
    }
  };

  const handleReset = () => {
    resetMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className='text-sm text-muted-foreground'>
        Loading SLA configuration...
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='grid grid-cols-2 gap-4'>
        <div className='space-y-2'>
          <Label htmlFor='urgent'>Urgent Priority (hours)</Label>
          <Input
            id='urgent'
            type='number'
            min='1'
            max='168'
            value={formData.urgent_deadline_hours}
            onChange={(e) =>
              handleInputChange(
                'urgent_deadline_hours',
                parseInt(e.target.value) || 1
              )
            }
          />
          <p className='text-sm text-muted-foreground'>
            Deadline for urgent priority tickets
          </p>
        </div>

        <div className='space-y-2'>
          <Label htmlFor='high'>High Priority (hours)</Label>
          <Input
            id='high'
            type='number'
            min='1'
            max='168'
            value={formData.high_deadline_hours}
            onChange={(e) =>
              handleInputChange(
                'high_deadline_hours',
                parseInt(e.target.value) || 1
              )
            }
          />
          <p className='text-sm text-muted-foreground'>
            Deadline for high priority tickets
          </p>
        </div>

        <div className='space-y-2'>
          <Label htmlFor='medium'>Medium Priority (hours)</Label>
          <Input
            id='medium'
            type='number'
            min='1'
            max='168'
            value={formData.medium_deadline_hours}
            onChange={(e) =>
              handleInputChange(
                'medium_deadline_hours',
                parseInt(e.target.value) || 1
              )
            }
          />
          <p className='text-sm text-muted-foreground'>
            Deadline for medium priority tickets
          </p>
        </div>

        <div className='space-y-2'>
          <Label htmlFor='low'>Low Priority (hours)</Label>
          <Input
            id='low'
            type='number'
            min='1'
            max='168'
            value={formData.low_deadline_hours}
            onChange={(e) =>
              handleInputChange(
                'low_deadline_hours',
                parseInt(e.target.value) || 1
              )
            }
          />
          <p className='text-sm text-muted-foreground'>
            Deadline for low priority tickets
          </p>
        </div>
      </div>

      <div className='flex items-center justify-between rounded-lg border p-4'>
        <div className='space-y-0.5'>
          <Label className='text-base'>Enable Automatic Escalation</Label>
          <p className='text-sm text-muted-foreground'>
            Automatically escalate ticket priorities when SLA deadlines are
            exceeded
          </p>
        </div>
        <Switch
          checked={formData.escalation_enabled}
          onCheckedChange={(checked) =>
            handleInputChange('escalation_enabled', checked)
          }
        />
      </div>

      <div className='flex gap-3'>
        <Button type='submit' disabled={updateMutation.isPending}>
          {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
        </Button>

        <Button
          type='button'
          variant='outline'
          onClick={handleReset}
          disabled={resetMutation.isPending}
        >
          {resetMutation.isPending ? 'Resetting...' : 'Reset to Defaults'}
        </Button>
      </div>
    </form>
  );
}
