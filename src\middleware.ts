import { clerkMiddleware } from '@clerk/nextjs/server';
import { NextResponse, type NextRequest } from 'next/server';
import { generateCsp } from './lib/security';
import { getSubdomain } from './lib/domain';

// Define allowed subdomains (organization slugs)
const ALLOWED_SUBDOMAINS = ['quantumnest'];

export default clerkMiddleware(async (auth, req: NextRequest) => {
  const hostname = req.headers.get('host') || 'localhost:3000';
  const subdomain = getSubdomain(hostname);
  const pathname = req.nextUrl.pathname;
  const response = NextResponse.next();
  const nonce = crypto.randomUUID();
  const csp = generateCsp(nonce, hostname);

  response.headers.set('Content-Security-Policy', csp);
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set(
    'Strict-Transport-Security',
    'max-age=63072000; includeSubDomains; preload'
  );
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), payment=(), usb=(), interest-cohort=()'
  );
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Nonce', nonce);

  // Skip static files only
  if (pathname.startsWith('/_next') || pathname.includes('.')) {
    return response;
  }

  // Handle API routes - allow them to pass through with authentication context
  if (pathname.startsWith('/api/')) {
    return response;
  }

  // Localhost dev (no subdomain)
  if (hostname.includes('localhost') && !subdomain) {
    return response;
  }

  // Subdomain flows
  if (subdomain) {
    if (!ALLOWED_SUBDOMAINS.includes(subdomain)) {
      return NextResponse.rewrite(new URL('/not-found', req.url));
    }

    const { userId } = await auth();
    const isAuth = Boolean(userId);
    // ENTERPRISE SECURITY: Only these pages are accessible to unauthenticated users
    const authPages = [
      '/sign-in',
      '/sign-up',
      '/forgot-password',
      '/reset-password',
      '/change-password',
    ];
    const isAuthPage = authPages.some((p) => pathname.startsWith(p));

    if (isAuthPage) {
      if (
        isAuth &&
        ['/sign-in', '/sign-up'].some((p) => pathname.startsWith(p))
      ) {
        return NextResponse.redirect(new URL('/tickets', req.url));
      }
      return response;
    }

    // ENTERPRISE SECURITY: Strict protection for all protected routes
    const protectedRoutes = [
      '/tickets',
      '/dashboard',
      '/settings',
      '/admin',
      '/reports',
    ];
    const isProtectedRoute = protectedRoutes.some((route) =>
      pathname.startsWith(route)
    );

    if (isProtectedRoute) {
      if (!isAuth) {
        // SECURITY: Prevent any unauthorized access to protected routes
        return NextResponse.redirect(new URL('/sign-in', req.url));
      }
      return response;
    }

    if (pathname === '/') {
      return isAuth
        ? NextResponse.redirect(new URL('/tickets', req.url))
        : NextResponse.redirect(new URL('/sign-in', req.url));
    }

    // ENTERPRISE SECURITY: Final catch-all - redirect any unauthenticated access
    if (!isAuth) {
      // SECURITY: Absolutely no unauthorized access allowed to any page
      return NextResponse.redirect(new URL('/sign-in', req.url));
    }
  }

  return response;
});

export const config = {
  matcher: [
    // Include API routes for authentication
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
  ],
};
