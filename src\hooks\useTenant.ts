/**
 * Modern React Query Tenant Hooks - 2025 Optimized
 *
 * Replaces legacy Zustand server state with React Query patterns
 */

import { useQuery } from '@tanstack/react-query';
import { tenantQueryOptions } from '@/lib/query-options';

/**
 * Hook for resolving tenant data from subdomain
 * Replaces the server state portion of the legacy tenant store
 */
export const useTenantResolve = (subdomain: string, enabled = true) => {
  return useQuery({
    ...tenantQueryOptions.resolve(subdomain),
    enabled: enabled && !!subdomain,
  });
};
