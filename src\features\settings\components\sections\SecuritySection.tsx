'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { PasswordChangeForm } from '../PasswordChangeForm';

export function SecuritySection() {
  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>Security</h2>
        <p className='text-sm text-muted-foreground'>
          Manage your account security and password.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Change Password</CardTitle>
          <CardDescription>
            Update your password to keep your account secure.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PasswordChangeForm />
        </CardContent>
      </Card>
    </div>
  );
}
