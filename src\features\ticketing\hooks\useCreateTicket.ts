import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CreateTicketFormSchema,
  CreateTicketFormData,
} from '../models/ticket-form.schema';
import { useDraftPersistence } from './useDraftPersistence';
import { toast } from '@/features/shared/components/toast';
import { UploadedFile } from '@/features/shared/components/DynamicFileUpload';
import { useActiveDepartments } from '@/features/departments/hooks/useDepartments';
import { useTenantUuid } from '@/hooks/useRealtimeQuery';

/**
 * Validates if auto-assignment is available for the given department and tenant
 */
async function validateAutoAssignment(
  tenantId: string,
  department: string
): Promise<boolean> {
  try {
    const response = await fetch('/api/auto-assignment/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tenant_id: tenantId,
        department,
      }),
    });

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.canAutoAssign || false;
  } catch (error) {
    console.error('Auto-assignment validation error:', error);
    return false;
  }
}

/**
 * Hook for managing the create ticket form.
 *
 * @param tenantId - The ID of the current tenant.
 * @param onSubmit - The function to call when the form is submitted.
 * @param userRole - The role of the current user (for role-based validation).
 * @returns An object with the form, state, and handlers.
 */
export function useCreateTicket(
  tenantId: string | null,
  onSubmit: (
    data: CreateTicketFormData & { attachment_ids: string[] }
  ) => Promise<void>,
  userRole?: string
) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  // Get tenant UUID for department validation
  const tenantUuidQuery = useTenantUuid(tenantId || '');
  const tenantUuid = tenantUuidQuery.data;

  // Get active departments for validation
  const { data: activeDepartments } = useActiveDepartments(tenantUuid || null);

  const form = useForm<CreateTicketFormData>({
    resolver: zodResolver(CreateTicketFormSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: '', // Empty string for placeholder state
      department: '',
      assignedTo: undefined,
      cc: [],
    },
  });

  const { clearDraft, loadDraftIntoForm } = useDraftPersistence(form);

  // Load draft when component mounts
  useEffect(() => {
    loadDraftIntoForm();
  }, [loadDraftIntoForm]);

  // Auto-reset logic for invalid department selections with real-time detection
  useEffect(() => {
    if (!activeDepartments || activeDepartments.length === 0) return;

    const currentDepartment = form.getValues('department');

    // Skip if no department is currently selected
    if (!currentDepartment) return;

    // Check if current department is still valid (active)
    const isCurrentDepartmentValid = activeDepartments.some(
      (dept) => dept.id === currentDepartment
    );

    // If current department is invalid, auto-reset to placeholder with feedback
    if (!isCurrentDepartmentValid) {
      console.log(
        '🔄 Auto-reset: Selected department is no longer active, resetting to placeholder'
      );
      console.log('📋 Current department ID:', currentDepartment);
      console.log(
        '📋 Available departments:',
        activeDepartments.map((d) => ({ id: d.id, name: d.name }))
      );

      // Reset to placeholder state
      form.setValue('department', '');

      // Enhanced user feedback for real-time deactivation
      console.log(
        '✅ Department selection automatically reset due to real-time deactivation'
      );
      console.log('💡 User can now select from available active departments');
    }
  }, [activeDepartments, form]);

  const handleSubmit = useCallback(
    async (data: CreateTicketFormData) => {
      if (!tenantId) {
        toast.error('Error', {
          description: 'Tenant information not available',
          duration: 4000,
        });
        return;
      }

      // Validate required fields with user-friendly messages
      const missingFields = [];

      if (!data.department || data.department === '') {
        missingFields.push('department');
      }

      if (!data.priority || data.priority === '') {
        missingFields.push('priority');
      }

      // Show appropriate toast message based on missing fields
      if (missingFields.length > 0) {
        if (missingFields.length === 2) {
          toast.error('Please select department and priority', {
            description:
              'Both department and priority are required to create a ticket.',
            duration: 4000,
          });
        } else if (missingFields.includes('department')) {
          toast.error('Please select a department', {
            description: 'A department is required to create a ticket.',
            duration: 4000,
          });
        } else if (missingFields.includes('priority')) {
          toast.error('Please select a priority', {
            description: 'A priority level is required to create a ticket.',
            duration: 4000,
          });
        }
        return;
      }

      try {
        // Validate that the selected department is still active
        if (data.department && activeDepartments) {
          const selectedDepartment = activeDepartments.find(
            (dept) => dept.id === data.department
          );

          if (!selectedDepartment) {
            // Department has been deactivated or doesn't exist
            toast.error('Department No Longer Available', {
              description:
                'This department has been deactivated and cannot be selected. Please choose a different department.',
              duration: 5000,
            });

            // Clear the department selection to force user to select a new one
            form.setValue('department', '');
            return;
          }
        }

        // Only validate assignment for admin/super_admin roles
        // Regular users should be able to create tickets without assignment constraints
        if (
          !data.assignedTo &&
          (userRole === 'admin' || userRole === 'super_admin')
        ) {
          const canAutoAssign = await validateAutoAssignment(
            tenantId,
            data.department
          );
          if (!canAutoAssign) {
            toast.error('Assignment Required', {
              description:
                'No auto-assignment rules are configured for this department and no default agent is set. Please assign the ticket to an agent manually.',
              duration: 4000,
            });
            return;
          }
        }

        clearDraft();

        // Use the provided onSubmit callback instead of calling createTicket directly
        await onSubmit({ ...data, attachment_ids: [] });
        form.reset();
        setUploadedFiles([]);
      } catch (error) {
        console.error('Failed to create ticket:', error);
        // Toast notification is handled by the parent component (useTicketWorkflow)
        // to avoid duplicate notifications
        throw error; // Re-throw to let parent handle the error
      }
    },
    [clearDraft, form, tenantId, onSubmit, userRole]
  );

  const handleDiscard = useCallback(() => {
    clearDraft();
    setUploadedFiles([]);
    form.reset();
  }, [clearDraft, form]);

  return {
    form,
    uploadedFiles,
    setUploadedFiles,
    handleSubmit,
    handleDiscard,
  };
}
