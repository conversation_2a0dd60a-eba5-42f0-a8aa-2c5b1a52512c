/**
 * Status Transition Utilities
 * Centralized business logic for ticket status transitions
 * @version 1.0 - React 19 Enterprise Implementation (January 2025)
 */

import { TicketStatus } from '../models/ticket.schema';

export type UserRole = 'user' | 'agent' | 'admin' | 'super_admin';

// Valid status transitions map - defines which statuses can transition to which other statuses
export const VALID_TRANSITIONS: Record<TicketStatus, TicketStatus[]> = {
  new: ['open'],
  open: ['pending', 'resolved', 'closed'],
  pending: ['open', 'resolved', 'closed'],
  resolved: ['open', 'closed'],
  closed: [], // Closed tickets cannot be transitioned
};

// Role-based transition permissions - defines which roles can set which statuses
export const ROLE_PERMISSIONS: Record<UserRole, TicketStatus[]> = {
  user: [], // Users cannot directly change status (status changes through message replies)
  agent: ['open', 'pending', 'resolved', 'closed'],
  admin: ['open', 'pending', 'resolved', 'closed'],
  super_admin: ['open', 'pending', 'resolved', 'closed'],
};

// Status transition triggers - defines what actions trigger status changes
export const STATUS_TRIGGERS = {
  // Agent opens a new ticket
  AGENT_OPENS_TICKET: {
    from: 'new' as TicketStatus,
    to: 'open' as TicketStatus,
    requiredRole: ['agent', 'admin', 'super_admin'] as UserRole[],
  },
  // Agent replies to ticket
  AGENT_REPLIES: {
    from: ['open', 'pending'] as TicketStatus[],
    to: 'pending' as TicketStatus,
    requiredRole: ['agent', 'admin', 'super_admin'] as UserRole[],
  },
  // User replies to ticket
  USER_REPLIES: {
    from: ['pending'] as TicketStatus[],
    to: 'open' as TicketStatus,
    requiredRole: ['user'] as UserRole[],
  },
  // Agent resolves ticket
  AGENT_RESOLVES: {
    from: ['open', 'pending'] as TicketStatus[],
    to: 'resolved' as TicketStatus,
    requiredRole: ['agent', 'admin', 'super_admin'] as UserRole[],
  },
  // Manual status change
  MANUAL_CHANGE: {
    from: ['new', 'open', 'pending', 'resolved'] as TicketStatus[],
    to: ['open', 'pending', 'resolved', 'closed'] as TicketStatus[],
    requiredRole: ['agent', 'admin', 'super_admin'] as UserRole[],
  },
} as const;

/**
 * Validation result interface
 */
export interface StatusTransitionValidation {
  isValid: boolean;
  error?: string;
  warning?: string;
}

/**
 * Status transition context
 */
export interface StatusTransitionContext {
  currentStatus: TicketStatus;
  newStatus: TicketStatus;
  userRole: UserRole;
  isAssignedAgent?: boolean;
  isTicketCreator?: boolean;
  hasResolveMessage?: boolean;
}

/**
 * Validates if a status transition is allowed
 */
export function validateStatusTransition(
  context: StatusTransitionContext
): StatusTransitionValidation {
  const { currentStatus, newStatus, userRole, isAssignedAgent } = context;

  // Check if the transition is valid for the current status
  const allowedTransitions = VALID_TRANSITIONS[currentStatus] || [];
  if (!allowedTransitions.includes(newStatus)) {
    return {
      isValid: false,
      error: `Cannot transition from ${currentStatus} to ${newStatus}`,
    };
  }

  // Check if the user role has permission for this transition
  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  if (!rolePermissions.includes(newStatus)) {
    return {
      isValid: false,
      error: `Role ${userRole} does not have permission to set status to ${newStatus}`,
    };
  }

  // Additional business rule validations
  if (newStatus === 'open' && userRole === 'agent') {
    // Agents can only open tickets assigned to them
    if (!isAssignedAgent) {
      return {
        isValid: false,
        error: 'You can only open tickets assigned to you',
      };
    }
  }

  // Warn about potential issues
  if (newStatus === 'closed' && currentStatus !== 'resolved') {
    return {
      isValid: true,
      warning:
        'Closing ticket without resolving first. Consider resolving before closing.',
    };
  }

  return { isValid: true };
}

/**
 * Gets the next possible statuses for a given current status and user role
 */
export function getAvailableTransitions(
  currentStatus: TicketStatus,
  userRole: UserRole,
  isAssignedAgent: boolean = false
): TicketStatus[] {
  const allowedTransitions = VALID_TRANSITIONS[currentStatus] || [];
  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];

  return allowedTransitions.filter((status) => {
    // Check role permissions
    if (!rolePermissions.includes(status)) {
      return false;
    }

    // Additional business rules
    if (status === 'open' && userRole === 'agent' && !isAssignedAgent) {
      return false;
    }

    return true;
  });
}

/**
 * Determines if a status change should be triggered by a message reply
 */
export function getStatusFromMessageReply(
  currentStatus: TicketStatus,
  userRole: UserRole,
  isResolveAction: boolean = false
): TicketStatus | null {
  // Agent resolves ticket
  if (isResolveAction && ['agent', 'admin', 'super_admin'].includes(userRole)) {
    if (['open', 'pending'].includes(currentStatus)) {
      return 'resolved';
    }
  }

  // Agent replies (without resolve)
  if (
    ['agent', 'admin', 'super_admin'].includes(userRole) &&
    !isResolveAction
  ) {
    if (['open', 'pending'].includes(currentStatus)) {
      return 'pending';
    }
  }

  // User replies
  if (userRole === 'user') {
    if (currentStatus === 'pending') {
      return 'open';
    }
    // Resolved tickets should remain resolved when users reply
    // Only manual agent intervention can change status from resolved
  }

  return null; // No status change needed
}

/**
 * Checks if a status is considered "active" (not closed)
 */
export function isActiveStatus(status: TicketStatus): boolean {
  return status !== 'closed';
}

/**
 * Checks if a status allows replies
 */
export function allowsReplies(
  status: TicketStatus,
  userRole: UserRole
): boolean {
  // Closed tickets don't allow replies
  if (status === 'closed') {
    return false;
  }

  // New tickets only allow replies from agents (which opens them)
  if (status === 'new') {
    return ['agent', 'admin', 'super_admin'].includes(userRole);
  }

  // All other statuses allow replies
  return true;
}

/**
 * Gets the display label for a status
 */
export function getStatusLabel(status: TicketStatus): string {
  const labels: Record<TicketStatus, string> = {
    new: 'New',
    open: 'Open',
    pending: 'Pending',
    resolved: 'Resolved',
    closed: 'Closed',
  };

  return labels[status] || status;
}

/**
 * Gets the color class for a status badge
 */
export function getStatusColor(status: TicketStatus): string {
  const colors: Record<TicketStatus, string> = {
    new: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    open: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    pending:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    resolved:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    closed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  };

  return colors[status] || 'bg-gray-100 text-gray-800';
}

/**
 * Checks if a resolve checkbox should be shown for the current context
 */
export function shouldShowResolveCheckbox(
  status: TicketStatus,
  userRole: UserRole,
  isAssignedAgent: boolean = false
): boolean {
  // Only show for open or pending tickets
  if (!['open', 'pending'].includes(status)) {
    return false;
  }

  // Show for agents, admins, and super_admins on open/pending tickets
  // Assignment check: show for assigned users or admins/super_admins
  if (isAssignedAgent || ['admin', 'super_admin'].includes(userRole)) {
    return true;
  }

  return false;
}

/**
 * Checks if the resolve checkbox should be disabled
 */
export function isResolveCheckboxDisabled(status: TicketStatus): boolean {
  return status === 'resolved';
}

/**
 * Gets the appropriate action buttons for a ticket status and user role
 */
export function getAvailableActions(
  status: TicketStatus,
  userRole: UserRole,
  isAssignedAgent: boolean = false
): Array<{
  action: string;
  label: string;
  variant?: 'default' | 'destructive' | 'outline';
}> {
  const actions: Array<{
    action: string;
    label: string;
    variant?: 'default' | 'destructive' | 'outline';
  }> = [];

  // Get available status transitions
  const availableTransitions = getAvailableTransitions(
    status,
    userRole,
    isAssignedAgent
  );

  // Map transitions to action buttons
  availableTransitions.forEach((targetStatus) => {
    switch (targetStatus) {
      case 'open':
        if (status === 'new') {
          actions.push({ action: 'open', label: 'Open Ticket' });
        } else {
          actions.push({ action: 'reopen', label: 'Reopen' });
        }
        break;
      case 'resolved':
        actions.push({ action: 'resolve', label: 'Resolve' });
        break;
      case 'closed':
        actions.push({
          action: 'close',
          label: 'Close',
          variant: 'destructive',
        });
        break;
    }
  });

  return actions;
}
