import { z } from 'zod';

export const TenantSchema = z.object({
  id: z.string(),
  name: z.string(),
  subdomain: z.string(),
  domain: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  settings: z
    .object({
      allowSignup: z.boolean().default(true),
      requireEmailVerification: z.boolean().default(true),
      maxUsers: z.number().optional(),
      features: z.array(z.string()).default([]),
    })
    .default(() => ({
      allowSignup: true,
      requireEmailVerification: false,
      features: [],
    })),
});

export const TenantUserSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  userId: z.string(),
  role: z.enum(['admin', 'agent', 'user']),
  permissions: z.array(z.string()).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Tenant = z.infer<typeof TenantSchema>;
export type TenantUser = z.infer<typeof TenantUserSchema>;

// Mock data removed for production
