'use client';

import { But<PERSON> } from '@/features/shared/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

/**
 * Error boundary specifically for Settings components
 */
export class SettingsErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Settings Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  override render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Card className='max-w-md mx-auto mt-8'>
          <CardHeader>
            <div className='flex items-center gap-2'>
              <AlertTriangle className='h-5 w-5 text-destructive' />
              <CardTitle>Settings Error</CardTitle>
            </div>
            <CardDescription>
              Something went wrong while loading the settings.
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='text-sm text-muted-foreground'>
              {this.state.error?.message || 'An unexpected error occurred'}
            </div>

            <div className='flex gap-2'>
              <Button onClick={this.handleRetry} variant='outline' size='sm'>
                <RefreshCw className='h-4 w-4 mr-2' />
                Try Again
              </Button>
              <Button
                onClick={() => window.location.reload()}
                variant='default'
                size='sm'
              >
                Reload Page
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
              <details className='mt-4'>
                <summary className='text-sm font-medium cursor-pointer'>
                  Error Details (Development)
                </summary>
                <pre className='mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-32'>
                  {this.state.error?.stack}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error boundary for functional components
 */
export function useSettingsErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      // Report error to monitoring service if available
      if (
        typeof window !== 'undefined' &&
        (window as { gtag?: (...args: unknown[]) => void }).gtag
      ) {
        (window as unknown as { gtag: (...args: unknown[]) => void }).gtag(
          'event',
          'exception',
          {
            description: error.message,
            fatal: false,
          }
        );
      }
    }
  }, [error]);

  return {
    error,
    resetError,
    handleError,
    hasError: !!error,
  };
}

/**
 * Loading skeleton for settings sections
 */
export function SettingsLoadingSkeleton() {
  return (
    <div className='space-y-6'>
      {/* Header skeleton */}
      <div className='space-y-2'>
        <div className='h-6 bg-muted rounded w-32 animate-pulse' />
        <div className='h-4 bg-muted rounded w-64 animate-pulse' />
      </div>

      {/* Card skeleton */}
      <Card>
        <CardHeader>
          <div className='h-5 bg-muted rounded w-40 animate-pulse' />
          <div className='h-4 bg-muted rounded w-56 animate-pulse' />
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-2'>
            <div className='h-4 bg-muted rounded w-24 animate-pulse' />
            <div className='h-10 bg-muted rounded animate-pulse' />
          </div>
          <div className='space-y-2'>
            <div className='h-4 bg-muted rounded w-32 animate-pulse' />
            <div className='h-10 bg-muted rounded animate-pulse' />
          </div>
          <div className='flex justify-end'>
            <div className='h-9 bg-muted rounded w-20 animate-pulse' />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Error state component for individual settings sections
 */
export function SettingsErrorState({
  error,
  onRetry,
  title = 'Error Loading Settings',
}: {
  error: string;
  onRetry: () => void;
  title?: string;
}) {
  return (
    <Card>
      <CardContent className='pt-6'>
        <div className='text-center space-y-4'>
          <AlertTriangle className='h-8 w-8 text-destructive mx-auto' />
          <div>
            <h3 className='font-medium'>{title}</h3>
            <p className='text-sm text-muted-foreground mt-1'>{error}</p>
          </div>
          <Button onClick={onRetry} variant='outline' size='sm'>
            <RefreshCw className='h-4 w-4 mr-2' />
            Try Again
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Success state component for settings operations
 */
export function SettingsSuccessState({
  message,
  onDismiss,
}: {
  message: string;
  onDismiss?: () => void;
}) {
  return (
    <div className='bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <div className='h-4 w-4 bg-green-500 rounded-full' />
          <span className='text-sm font-medium text-green-800 dark:text-green-200'>
            {message}
          </span>
        </div>
        {onDismiss && (
          <Button
            onClick={onDismiss}
            variant='ghost'
            size='sm'
            className='h-auto p-1 text-green-600 hover:text-green-800'
          >
            ×
          </Button>
        )}
      </div>
    </div>
  );
}

/**
 * Performance monitoring component
 */
export function SettingsPerformanceIndicator({
  isLoading,
  loadTime,
}: {
  isLoading: boolean;
  loadTime?: number;
}) {
  if (!process.env.NODE_ENV || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className='fixed bottom-4 right-4 bg-background border rounded-lg p-2 text-xs font-mono shadow-lg'>
      <div className='flex items-center gap-2'>
        <div
          className={`h-2 w-2 rounded-full ${isLoading ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'}`}
        />
        <span>
          Settings: {isLoading ? 'Loading...' : `${loadTime?.toFixed(0)}ms`}
        </span>
      </div>
    </div>
  );
}
