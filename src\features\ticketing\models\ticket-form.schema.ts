import { z } from 'zod';

// Form schema allows empty strings for placeholders
export const CreateTicketFormSchema = z.object({
  title: z.string().max(200, 'Title too long'),
  description: z.string(),
  priority: z.union([
    z.enum(['low', 'medium', 'high', 'urgent']),
    z.literal(''),
  ]),
  department: z.string(),
  assignedTo: z.string().optional(),
  cc: z.array(z.string()),
});

// Validation schema for submission (requires valid values)
export const CreateTicketValidationSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required'),
  priority: z
    .enum(['low', 'medium', 'high', 'urgent'])
    .refine(
      (val) => ['low', 'medium', 'high', 'urgent'].includes(val),
      'Please select a priority'
    ),
  department: z.string().min(1, 'Please select a department'),
  assignedTo: z.string().optional(),
  cc: z.array(z.string()),
});

export type CreateTicketFormData = z.infer<typeof CreateTicketFormSchema>;
