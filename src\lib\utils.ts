import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getInitials = (name: string): string => {
  if (!name) return 'U';
  return (
    name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase() || 'U'
  );
};

/**
 * Counts meaningful words in a string, handling both plain text and HTML content
 * Used for consistent word counting across ticket creation validation and draft confirmation
 *
 * @param text - The text to count words in (can contain HTML)
 * @returns Number of meaningful words
 */
export function countWords(text: string): number {
  if (!text || typeof text !== 'string') return 0;

  // Strip HTML tags and decode HTML entities for rich text content
  const strippedText = text
    .replace(/<[^>]*>/g, ' ') // Remove HTML tags
    .replace(/&[^;]+;/g, ' ') // Remove HTML entities
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  if (!strippedText) return 0;

  // Split by whitespace and filter out empty strings
  return strippedText.split(/\s+/).filter((word) => word.length > 0).length;
}
