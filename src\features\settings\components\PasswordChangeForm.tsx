'use client';

import { Button } from '@/features/shared/components/ui/button';
import { Input } from '@/features/shared/components/ui/input';
import { Label } from '@/features/shared/components/ui/label';
import { cn } from '@/lib/utils';
import { useUser } from '@clerk/nextjs';
import { zodResolver } from '@hookform/resolvers/zod';
import { Check, Eye, EyeOff, Lock, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import {
  PasswordChangeSchema,
  type PasswordChange,
} from '../models/settings.schema';

interface PasswordRequirement {
  label: string;
  test: (password: string) => boolean;
}

const passwordRequirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8,
  },
  {
    label: 'One uppercase letter',
    test: (password) => /[A-Z]/.test(password),
  },
  {
    label: 'One lowercase letter',
    test: (password) => /[a-z]/.test(password),
  },
  {
    label: 'One number',
    test: (password) => /\d/.test(password),
  },
];

export function PasswordChangeForm() {
  const { user } = useUser();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isValid },
  } = useForm<PasswordChange>({
    resolver: zodResolver(PasswordChangeSchema),
    mode: 'onChange',
  });

  const newPassword = watch('new_password', '');

  const onSubmit = async (data: PasswordChange) => {
    if (!user) {
      toast.error('User not found');
      return;
    }

    setIsLoading(true);

    try {
      // Use Clerk's updatePassword method
      await user.updatePassword({
        currentPassword: data.current_password,
        newPassword: data.new_password,
      });

      toast.success('Password updated successfully');
      reset();
    } catch (error: unknown) {
      // Handle specific Clerk errors
      const clerkError = error as { errors?: Array<{ code: string }> };
      if (clerkError.errors?.[0]?.code === 'form_password_incorrect') {
        toast.error('Current password is incorrect');
      } else if (clerkError.errors?.[0]?.code === 'form_password_pwned') {
        toast.error(
          'This password has been found in a data breach. Please choose a different password.'
        );
      } else if (
        clerkError.errors?.[0]?.code === 'form_password_validation_failed'
      ) {
        toast.error('New password does not meet security requirements');
      } else {
        toast.error('Failed to update password. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
      {/* Current Password */}
      <div className='space-y-2'>
        <Label htmlFor='current_password'>Current Password</Label>
        <div className='relative'>
          <Input
            id='current_password'
            type={showCurrentPassword ? 'text' : 'password'}
            placeholder='Enter your current password'
            {...register('current_password')}
            className={cn(
              'pr-10',
              errors.current_password && 'border-red-500 focus:border-red-500'
            )}
          />
          <Button
            type='button'
            variant='ghost'
            size='sm'
            className='absolute right-0 top-0 h-full px-3 hover:bg-transparent'
            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
          >
            {showCurrentPassword ? (
              <EyeOff className='h-4 w-4 text-muted-foreground' />
            ) : (
              <Eye className='h-4 w-4 text-muted-foreground' />
            )}
          </Button>
        </div>
        {errors.current_password && (
          <p className='text-sm text-red-600'>
            {errors.current_password.message}
          </p>
        )}
      </div>

      {/* New Password */}
      <div className='space-y-2'>
        <Label htmlFor='new_password'>New Password</Label>
        <div className='relative'>
          <Input
            id='new_password'
            type={showNewPassword ? 'text' : 'password'}
            placeholder='Enter your new password'
            {...register('new_password')}
            className={cn(
              'pr-10',
              errors.new_password && 'border-red-500 focus:border-red-500'
            )}
          />
          <Button
            type='button'
            variant='ghost'
            size='sm'
            className='absolute right-0 top-0 h-full px-3 hover:bg-transparent'
            onClick={() => setShowNewPassword(!showNewPassword)}
          >
            {showNewPassword ? (
              <EyeOff className='h-4 w-4 text-muted-foreground' />
            ) : (
              <Eye className='h-4 w-4 text-muted-foreground' />
            )}
          </Button>
        </div>
        {errors.new_password && (
          <p className='text-sm text-red-600'>{errors.new_password.message}</p>
        )}
      </div>

      {/* Password Requirements */}
      {newPassword && (
        <div className='space-y-2'>
          <Label className='text-sm font-medium'>Password Requirements</Label>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-2'>
            {passwordRequirements.map((requirement, index) => {
              const isValid = requirement.test(newPassword);
              return (
                <div
                  key={index}
                  className={cn(
                    'flex items-center gap-2 text-sm',
                    isValid ? 'text-green-600' : 'text-muted-foreground'
                  )}
                >
                  {isValid ? (
                    <Check className='h-3 w-3' />
                  ) : (
                    <X className='h-3 w-3' />
                  )}
                  <span>{requirement.label}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Confirm Password */}
      <div className='space-y-2'>
        <Label htmlFor='confirm_password'>Confirm New Password</Label>
        <div className='relative'>
          <Input
            id='confirm_password'
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder='Confirm your new password'
            {...register('confirm_password')}
            className={cn(
              'pr-10',
              errors.confirm_password && 'border-red-500 focus:border-red-500'
            )}
          />
          <Button
            type='button'
            variant='ghost'
            size='sm'
            className='absolute right-0 top-0 h-full px-3 hover:bg-transparent'
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {showConfirmPassword ? (
              <EyeOff className='h-4 w-4 text-muted-foreground' />
            ) : (
              <Eye className='h-4 w-4 text-muted-foreground' />
            )}
          </Button>
        </div>
        {errors.confirm_password && (
          <p className='text-sm text-red-600'>
            {errors.confirm_password.message}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <div className='flex items-center gap-3'>
        <Button
          type='submit'
          disabled={!isValid || isLoading}
          className='min-w-[120px]'
        >
          {isLoading ? (
            <div className='flex items-center gap-2'>
              <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
              Updating...
            </div>
          ) : (
            <>
              <Lock className='w-4 h-4 mr-2' />
              Update Password
            </>
          )}
        </Button>

        <Button
          type='button'
          variant='outline'
          onClick={() => reset()}
          disabled={isLoading}
        >
          Cancel
        </Button>
      </div>

      {/* Security Notice */}
      <div className='bg-muted/50 rounded-lg p-4 space-y-2'>
        <h4 className='text-sm font-medium flex items-center gap-2'>
          <Lock className='w-4 h-4' />
          Security Tips
        </h4>
        <ul className='text-xs text-muted-foreground space-y-1'>
          <li>• Use a unique password that you don&apos;t use elsewhere</li>
          <li>• Consider using a password manager</li>
          <li>• Avoid common words, names, or personal information</li>
          <li>• Change your password regularly</li>
        </ul>
      </div>
    </form>
  );
}
