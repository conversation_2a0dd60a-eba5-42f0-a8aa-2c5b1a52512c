/**
 * Custom React Query Hooks for Users - 2025 Optimized
 *
 * These hooks abstract all user-related data-fetching logic
 * Following TkDodo's best practices for React Query v5
 */

import { useQuery } from '@tanstack/react-query';
import { userQueryOptions } from '@/lib/query-options';
import { QueryKeys } from '@/lib/query-keys';

interface UserSearchResult {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
  avatar_url?: string;
  clerk_id: string;
}

interface UserInfo {
  id: string;
  clerk_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  status: string;
  tenant_id: string;
  created_at: string;
  last_login_at?: string;
}

interface UserInfoResponse {
  success: boolean;
  user: UserInfo;
  tenant: unknown;
}

// Custom hook for user search with debouncing and minimum query length
export const useUserSearch = (tenantId: string, query: string, limit = 10) => {
  return useQuery({
    ...userQueryOptions.search(tenantId, query, limit),
    enabled: !!tenantId && query.length >= 3, // Only search when query is at least 3 characters
  });
};

// Custom hook for current user info
export const useUserInfo = () => {
  return useQuery(userQueryOptions.info());
};

// Custom hook for getting user by ID (uses React Query patterns)
export const useUser = (tenantId: string, userId: string) => {
  return useQuery({
    ...userQueryOptions.search(tenantId, userId, 1),
    select: (data) => data[0] || null,
    enabled: !!tenantId && !!userId,
  });
};

// Custom hook for getting multiple users by IDs (simplified - uses individual queries)
export const useUsers = (tenantId: string, userIds: string[]) => {
  return useQuery({
    queryKey: [...QueryKeys.USERS.all(tenantId), 'batch', userIds.sort()],
    queryFn: async () => {
      if (userIds.length === 0) return [];

      // Simplified implementation - React Query will handle caching and deduplication
      // In production, consider implementing a batch API endpoint
      const userPromises = userIds.map(async (userId) => {
        try {
          // Direct API call - will be replaced with proper React Query API functions
          const response = await fetch(
            `/api/users/search?q=${userId}&limit=1`,
            {
              headers: { 'Content-Type': 'application/json' },
            }
          );
          if (!response.ok) return null;
          const data = await response.json();
          return data.users?.[0] || null;
        } catch {
          return null;
        }
      });

      const users = await Promise.all(userPromises);
      return users.filter(Boolean) as UserSearchResult[];
    },
    enabled: !!tenantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Custom hook for getting users by role (simplified - uses search API)
export const useUsersByRole = (tenantId: string, roles: string[]) => {
  return useQuery({
    queryKey: [...QueryKeys.USERS.all(tenantId), 'by-role', roles.sort()],
    queryFn: async () => {
      // Direct API call with role filter - React Query handles caching
      const params = new URLSearchParams({
        role: roles.join(','),
        limit: '50',
      });
      const response = await fetch(`/api/users/search?${params.toString()}`, {
        headers: { 'Content-Type': 'application/json' },
      });
      if (!response.ok) {
        throw new Error(
          `Failed to fetch users by role: ${response.statusText}`
        );
      }
      const data = await response.json();
      return data.users || [];
    },
    enabled: !!tenantId && roles.length > 0,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Export types for use in components
export type { UserSearchResult, UserInfo, UserInfoResponse };
