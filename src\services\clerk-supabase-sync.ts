import {
  createServerSupabaseClient,
  createServiceSupabaseClient,
} from '@/lib/supabase-server';
import type { Database } from '@/types/supabase';

type User = Database['public']['Tables']['users']['Insert'];

export interface ClerkOrganization {
  id: string;
  name: string;
  slug: string;
  imageUrl?: string;
  createdAt: number;
  membersCount?: number | undefined;
}

export interface ClerkUser {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  fullName?: string | null;
  primaryEmailAddress?: {
    emailAddress: string;
  } | null;
  imageUrl?: string;
  createdAt?: number;
  publicMetadata?: {
    role?: string;
  };
}

export interface SyncResult {
  success: boolean;
  tenant?: Database['public']['Tables']['tenants']['Row'];
  user?: Database['public']['Tables']['users']['Row'];
  error?: string;
}

export class ClerkSupabaseSync {
  private supabase;

  constructor(authToken: string) {
    this.supabase = createServerSupabaseClient(authToken);
  }

  /**
   * Sync Clerk organization to Supabase tenant using an upsert operation.
   */
  async syncTenant(
    organization: ClerkOrganization
  ): Promise<Database['public']['Tables']['tenants']['Row']> {
    const tenantData = {
      name: organization.name,
      subdomain: organization.slug,
      status: 'active' as const,
      settings: {
        features: ['tickets', 'analytics'],
        branding: {
          logo: organization.imageUrl || null,
        },
      },
      created_at: new Date(organization.createdAt).toISOString(),
    };

    // Use service role client to bypass RLS for tenant creation/update.
    const serviceClient = createServiceSupabaseClient();
    const { data: tenant, error } = await serviceClient
      .from('tenants')
      .upsert(tenantData, { onConflict: 'subdomain' })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to sync tenant: ${error.message}`);
    }

    return tenant;
  }

  /**
   * Sync Clerk user to Supabase user using an upsert operation.
   */
  async syncUser(
    user: ClerkUser,
    tenant: Database['public']['Tables']['tenants']['Row']
  ): Promise<Database['public']['Tables']['users']['Row']> {
    // Comprehensive role mapping to handle all Clerk roles, including fallbacks.
    const clerkRole = (user.publicMetadata?.role as string) || 'user';
    const roleMapping: Record<string, string> = {
      // Official Clerk organization role keys
      'org:super_admin': 'super_admin',
      'org:admin': 'admin',
      'org:agent': 'agent',
      'org:member': 'user',

      // Fallback for display names or other variations
      'super admin': 'super_admin',
      super_admin: 'super_admin',
      admin: 'admin',
      agent: 'agent',
      member: 'user',
      user: 'user',
    };

    // Normalize the role from Clerk to ensure it's a key in our map
    const userRole = roleMapping[clerkRole.toLowerCase()] || 'user';

    const userData: User = {
      clerk_id: user.id,
      email: user.primaryEmailAddress?.emailAddress || '',
      first_name: user.firstName || null,
      last_name: user.lastName || null,
      role: userRole,
      status: 'active',
      tenant_id: tenant.id,
      // Always sync the latest avatar URL from Clerk to ensure fresh images
      avatar_url: user.imageUrl || null,
      last_login_at: new Date().toISOString(),
      created_at: new Date(user.createdAt || Date.now()).toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Use service role client to bypass RLS for user creation/update.
    const serviceClient = createServiceSupabaseClient();
    const { data: syncedUser, error } = await serviceClient
      .from('users')
      .upsert(userData, { onConflict: 'clerk_id' })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to sync user: ${error.message}`);
    }

    return syncedUser;
  }

  /**
   * Retry wrapper for async operations
   */
  private async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        }
      }
    }

    throw lastError!;
  }

  /**
   * Ensure both tenant and user are synced with retry logic
   */
  async ensureSync(
    user: ClerkUser,
    organization: ClerkOrganization
  ): Promise<SyncResult> {
    try {
      // Sync tenant first with retry
      const tenant = await this.retryOperation(
        () => this.syncTenant(organization),
        3,
        1000
      );

      // Then sync user with retry
      const syncedUser = await this.retryOperation(
        () => this.syncUser(user, tenant),
        3,
        1000
      );

      return {
        success: true,
        tenant,
        user: syncedUser,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown sync error',
      };
    }
  }

  /**
   * Check if sync is needed (user/tenant doesn't exist in Supabase)
   */
  async checkSyncStatus(
    userId: string,
    tenantId: string
  ): Promise<{
    needsSync: boolean;
    tenantExists: boolean;
    userExists: boolean;
  }> {
    try {
      // Use service client to check tenant existence (bypasses RLS)
      const serviceClient = createServiceSupabaseClient();

      const [tenantResult, userResult] = await Promise.all([
        serviceClient
          .from('tenants')
          .select('id')
          .eq('subdomain', tenantId)
          .single(),
        this.supabase
          .from('users')
          .select('id')
          .eq('clerk_id', userId)
          .single(),
      ]);

      const tenantExists = !tenantResult.error;
      const userExists = !userResult.error;
      const needsSync = !tenantExists || !userExists;

      return {
        needsSync,
        tenantExists,
        userExists,
      };
    } catch {
      return {
        needsSync: true,
        tenantExists: false,
        userExists: false,
      };
    }
  }
}
