import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Statistics query schema
const StatisticsQuerySchema = z.object({
  days_back: z.coerce.number().int().min(1).max(365).default(30),
});

// Helper function to check admin permissions
async function checkAdminPermissions(userId: string) {
  const serviceClient = createServiceSupabaseClient();

  const { data: currentUser, error: userError } = await serviceClient
    .from('users')
    .select('id, tenant_id, role')
    .eq('clerk_id', userId)
    .single();

  if (userError || !currentUser) {
    throw new Error('User not found in database');
  }

  if (currentUser.role !== 'admin' && currentUser.role !== 'super_admin') {
    throw new Error('Insufficient permissions');
  }

  return currentUser;
}

// GET - Fetch SLA escalation statistics
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryValidation = StatisticsQuerySchema.safeParse({
      days_back: searchParams.get('days_back'),
    });

    if (!queryValidation.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: queryValidation.error.issues,
        },
        { status: 400 }
      );
    }

    const { days_back } = queryValidation.data;
    const serviceClient = createServiceSupabaseClient();

    // Get escalation statistics using the database function
    const { data: statistics, error: statsError } = await serviceClient.rpc(
      'get_escalation_statistics',
      {
        p_tenant_id: currentUser.tenant_id,
        p_days_back: days_back,
      }
    );

    if (statsError) {
      console.error('Statistics fetch error:', statsError);
      return NextResponse.json(
        { error: 'Failed to fetch escalation statistics' },
        { status: 500 }
      );
    }

    // Get additional metrics
    const { data: ticketMetrics, error: metricsError } = await serviceClient
      .from('tickets')
      .select('priority, status, created_at')
      .eq('tenant_id', currentUser.tenant_id)
      .gte(
        'created_at',
        new Date(Date.now() - days_back * 24 * 60 * 60 * 1000).toISOString()
      );

    if (metricsError) {
      console.error('Ticket metrics fetch error:', metricsError);
      return NextResponse.json(
        { error: 'Failed to fetch ticket metrics' },
        { status: 500 }
      );
    }

    // Calculate additional metrics
    const totalTickets = ticketMetrics?.length || 0;
    const priorityDistribution =
      ticketMetrics?.reduce(
        (acc, ticket) => {
          acc[ticket.priority] = (acc[ticket.priority] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    const statusDistribution =
      ticketMetrics?.reduce(
        (acc, ticket) => {
          acc[ticket.status] = (acc[ticket.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    // Get current SLA configuration
    const { data: slaConfig } = await serviceClient
      .from('sla_configurations')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    // Check cron job status
    const { data: cronStatus, error: cronError } = await serviceClient.rpc(
      'get_sla_cron_status'
    );

    if (cronError) {
      console.error('Cron status fetch error:', cronError);
    }

    const response = {
      escalation_statistics: statistics?.[0] || {
        total_escalations: 0,
        recent_executions: 0,
        last_execution: null,
        avg_execution_time_ms: 0,
        error_count: 0,
      },
      ticket_metrics: {
        total_tickets: totalTickets,
        priority_distribution: priorityDistribution,
        status_distribution: statusDistribution,
      },
      sla_configuration: slaConfig || null,
      cron_job_status: cronStatus?.[0] || null,
      period: {
        days_back,
        start_date: new Date(
          Date.now() - days_back * 24 * 60 * 60 * 1000
        ).toISOString(),
        end_date: new Date().toISOString(),
      },
    };

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error(
      'Unexpected error in GET /api/settings/sla/statistics:',
      error
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
