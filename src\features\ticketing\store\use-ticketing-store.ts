/**
 * Ticketing UI Store - 2025 Optimized for UI State Only
 *
 * Following documented patterns: <PERSON><PERSON><PERSON> handles ONLY UI state, React Query handles server state.
 * This store manages UI-specific state like selections, filters, and modal states.
 *
 * Key Features:
 * - UI-only state management (no server state duplication)
 * - Minimal re-renders with strategic selectors
 * - Clean separation of concerns
 * - Modern Zustand v5 patterns
 *
 * <AUTHOR> Augster
 * @version 4.0 - UI-Only Store (January 2025)
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// UI-only state interface - no server state
interface TicketingUIState {
  selectedTicketId: string | null;
  currentTenantId: string | null;

  // UI filters and search
  filters: {
    search: string;
    status: string[];
    priority: string[];
    assignedTo: string | null;
  };

  // UI state flags
  isCreatingTicket: boolean;
  expandedSections: Record<string, boolean>;
}

// UI-only actions interface
interface TicketingUIActions {
  // Selection actions
  setSelectedTicketId: (ticketId: string | null) => void;
  setCurrentTenantId: (tenantId: string | null) => void;

  // Filter actions
  setFilters: (filters: Partial<TicketingUIState['filters']>) => void;
  resetFilters: () => void;

  // UI state actions
  setIsCreatingTicket: (isCreating: boolean) => void;
  toggleSection: (sectionId: string) => void;
  setExpandedSections: (sections: Record<string, boolean>) => void;

  // Reset action
  resetUIState: () => void;
}

// Initial state
const initialUIState: TicketingUIState = {
  selectedTicketId: null,
  currentTenantId: null,
  filters: {
    search: '',
    status: [],
    priority: [],
    assignedTo: null,
  },
  isCreatingTicket: false,
  expandedSections: {
    new: false,
    assigned: true, // Default to expanded
    closed: false,
  },
};

export const useTicketingUIStore = create<
  TicketingUIState & TicketingUIActions
>()(
  devtools(
    persist(
      (set) => ({
        ...initialUIState,

        // Selection actions
        setSelectedTicketId: (ticketId) => set({ selectedTicketId: ticketId }),
        setCurrentTenantId: (tenantId) => set({ currentTenantId: tenantId }),

        // Filter actions
        setFilters: (newFilters) =>
          set((state) => ({
            filters: { ...state.filters, ...newFilters },
          })),

        resetFilters: () =>
          set({
            filters: initialUIState.filters,
          }),

        // UI state actions
        setIsCreatingTicket: (isCreating) =>
          set({ isCreatingTicket: isCreating }),

        toggleSection: (sectionId) =>
          set((state) => ({
            expandedSections: {
              ...state.expandedSections,
              [sectionId]: !state.expandedSections[sectionId],
            },
          })),

        setExpandedSections: (sections) => set({ expandedSections: sections }),

        // Reset action
        resetUIState: () => set(initialUIState),
      }),
      {
        name: 'ticketing-ui-store',
        partialize: (state) => ({
          currentTenantId: state.currentTenantId,
          expandedSections: state.expandedSections,
          // Don't persist selectedTicketId or filters - they should reset on page load
        }),
      }
    ),
    {
      name: 'ticketing-ui-store-devtools',
    }
  )
);

// Simple selectors for UI state
export const useTicketingUISelectors = {
  useSelectedTicketId: () =>
    useTicketingUIStore((state) => state.selectedTicketId),
  useCurrentTenantId: () =>
    useTicketingUIStore((state) => state.currentTenantId),
  useFilters: () => useTicketingUIStore((state) => state.filters),
  useIsCreatingTicket: () =>
    useTicketingUIStore((state) => state.isCreatingTicket),
  useExpandedSections: () =>
    useTicketingUIStore((state) => state.expandedSections),
};

// Individual action hooks to prevent re-render issues
export const useTicketingUIActions = {
  useSetSelectedTicketId: () =>
    useTicketingUIStore((state) => state.setSelectedTicketId),
  useSetCurrentTenantId: () =>
    useTicketingUIStore((state) => state.setCurrentTenantId),
  useSetFilters: () => useTicketingUIStore((state) => state.setFilters),
  useResetFilters: () => useTicketingUIStore((state) => state.resetFilters),
  useSetIsCreatingTicket: () =>
    useTicketingUIStore((state) => state.setIsCreatingTicket),
  useToggleSection: () => useTicketingUIStore((state) => state.toggleSection),
  useSetExpandedSections: () =>
    useTicketingUIStore((state) => state.setExpandedSections),
  useResetUIState: () => useTicketingUIStore((state) => state.resetUIState),
};
