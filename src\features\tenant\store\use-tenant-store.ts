import { create } from 'zustand';
import { useEffect, useState, useMemo } from 'react';
import { getDomainFromWindow, DomainInfoState } from '@/lib/domain';

// UI-only state interface - server state moved to React Query
interface TenantState {
  tenantId: string | null;
  domainInfo: DomainInfoState | null;
  isInitialized: boolean;
  actions: {
    initializeDomain: () => void;
    setTenantId: (tenantId: string | null) => void;
  };
}

export const useTenantStore = create<TenantState>((set) => ({
  tenantId: null,
  domainInfo: null,
  isInitialized: false,
  actions: {
    initializeDomain: () => {
      const info = getDomainFromWindow(window);
      set({
        domainInfo: info,
        tenantId: info.tenantId || null,
        isInitialized: true,
      });
    },

    setTenantId: (tenantId: string | null) => {
      set({ tenantId });
    },
  },
}));

// SSR-safe selector for UI state only
export const useTenant = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const tenantId = useTenantStore((state) => state.tenantId);
  const domainInfo = useTenantStore((state) => state.domainInfo);
  const isInitialized = useTenantStore((state) => state.isInitialized);

  // Memoize the return value to prevent unnecessary re-renders
  const result = useMemo(
    () => ({
      tenantId,
      domainInfo,
      isInitialized,
    }),
    [tenantId, domainInfo, isInitialized]
  );

  // Return safe defaults during SSR
  if (!isClient) {
    return {
      tenantId: null,
      domainInfo: null,
      isInitialized: false,
    };
  }

  return result;
};

export const useTenantActions = () => useTenantStore((state) => state.actions);
