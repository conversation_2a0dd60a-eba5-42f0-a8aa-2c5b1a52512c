/**
 * SafeHtml Component - Secure HTML Rendering
 *
 * Provides safe HTML rendering using DOMPurify sanitization to prevent XSS attacks
 * while preserving basic HTML formatting for ticket descriptions and content.
 *
 * <AUTHOR> Augster
 * @version 1.0 - Safe HTML Rendering (January 2025)
 */

'use client';

import React from 'react';
import {
  sanitizeHtml,
  stripHtml,
  hasHtmlTags,
} from '@/lib/utils/html-sanitizer';
import { cn } from '@/lib/utils';

interface SafeHtmlProps {
  /** HTML content to render safely */
  content: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to strip HTML and show plain text only */
  plainText?: boolean;
  /** Maximum length for truncation (only applies to plain text mode) */
  maxLength?: number;
  /** HTML element to render as (default: div) */
  as?: keyof React.JSX.IntrinsicElements;
  /** Additional props to pass to the rendered element */
  [key: string]: unknown;
}

/**
 * SafeHtml Component
 *
 * Safely renders HTML content with XSS protection via DOMPurify sanitization.
 * Automatically detects if content contains HTML and renders appropriately.
 *
 * @example
 * // Render HTML content safely
 * <SafeHtml content="<p>Hello <strong>world</strong>!</p>" />
 *
 * @example
 * // Render as plain text only
 * <SafeHtml content="<p>Hello world!</p>" plainText />
 *
 * @example
 * // Truncate plain text
 * <SafeHtml content="Long content..." plainText maxLength={50} />
 */
export function SafeHtml({
  content,
  className,
  plainText = false,
  maxLength,
  as = 'div',
  ...props
}: SafeHtmlProps) {
  const Component = as as React.ElementType;
  // Handle empty or invalid content
  if (!content || typeof content !== 'string') {
    return null;
  }

  // If plain text mode is requested, strip HTML
  if (plainText) {
    let textContent = stripHtml(content);

    // Apply truncation if specified
    if (maxLength && textContent.length > maxLength) {
      textContent = textContent.substring(0, maxLength) + '...';
    }

    return (
      <Component className={className} {...props}>
        {textContent}
      </Component>
    );
  }

  // Check if content actually contains HTML tags
  const containsHtml = hasHtmlTags(content);

  // If no HTML tags, render as plain text
  if (!containsHtml) {
    return (
      <Component className={className} {...props}>
        {content}
      </Component>
    );
  }

  // Sanitize HTML content for safe rendering
  const sanitizedHtml = sanitizeHtml(content);

  // If sanitization removed all content, render nothing
  if (!sanitizedHtml.trim()) {
    return null;
  }

  // Render sanitized HTML using dangerouslySetInnerHTML
  return (
    <Component
      className={cn('rich-content', className)}
      dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
      {...props}
    />
  );
}

/**
 * SafeHtmlInline Component
 *
 * Specialized version for inline HTML rendering (uses span by default)
 */
export function SafeHtmlInline({
  content,
  className,
  ...props
}: Omit<SafeHtmlProps, 'as'>) {
  return (
    <SafeHtml
      content={content as string}
      className={cn('inline', className as string)}
      as='span'
      {...props}
    />
  );
}

/**
 * SafeHtmlPreview Component
 *
 * Specialized version for previews (strips HTML and truncates)
 */
export function SafeHtmlPreview({
  content,
  maxLength = 150,
  className,
  ...props
}: Omit<SafeHtmlProps, 'plainText'>) {
  return (
    <SafeHtml
      content={content as string}
      className={className as string}
      plainText
      maxLength={maxLength as number}
      {...props}
    />
  );
}
