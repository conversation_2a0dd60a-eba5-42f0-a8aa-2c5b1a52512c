'use client';

import { Checkbox } from '@/features/shared/components/ui/checkbox';
import { TicketStatus } from '../models/ticket.schema';
import {
  shouldShowResolveCheckbox,
  isResolveCheckboxDisabled,
} from '../utils/status-transitions';
import type { UserRole } from '../utils/status-transitions';

interface ResolveCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  ticketStatus: TicketStatus;
  userRole: UserRole;
  isAssignedAgent?: boolean;
}

export function ResolveCheckbox({
  checked,
  onChange,
  disabled = false,
  ticketStatus,
  userRole,
  isAssignedAgent = false,
}: ResolveCheckboxProps) {
  // Don't render if the checkbox shouldn't be shown for this context
  if (!shouldShowResolveCheckbox(ticketStatus, userRole, isAssignedAgent)) {
    return null;
  }

  const isDisabled = disabled || isResolveCheckboxDisabled(ticketStatus);

  return (
    <Checkbox
      checked={checked}
      onChange={(e) => onChange(e.target.checked)}
      disabled={isDisabled}
      label='Mark as Resolved'
      description={
        ticketStatus === 'resolved'
          ? 'This ticket is already resolved'
          : 'Sets the ticket to Resolved when you send this reply'
      }
      className='transition-all duration-200'
    />
  );
}
