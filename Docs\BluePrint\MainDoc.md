# Part 1: Architecture & Technology Stack with Tenant Isolation & Hybrid Sync (Updated for React Query v5)

---

## 1. Project Goals

- Build a **robust, scalable, and maintainable multi-tenant support ticketing system** with **strict tenant data isolation**.
- Enable **ultra-fast, responsive UI** with **optimistic updates**, **persistent client caching**, and **hybrid data synchronization** leveraging Dexie.js, Supabase Realtime, Zustand, and **React Query (TanStack Query v5)**.
- Utilize a modern 2025 tech stack: Next.js 15, React 19, <PERSON><PERSON><PERSON>, Clerk, <PERSON>ustand, React Query, Zod, Tailwind CSS, and ShadCN UI.
- Design for **easy extensibility**, including future AI integrations, enhanced notifications, and approval workflows.
- Ensure **offline-first capability** with smooth syncing and conflict resolution.
- Provide **real-time collaboration** and instant UI updates via Supabase Realtime.

---

## 2. Updated Technology Stack Overview

| Layer              | Technology       | Purpose                                                                                         |
| ------------------ | ---------------- | ----------------------------------------------------------------------------------------------- |
| Frontend Framework | Next.js 15.3.4   | React SSR, routing, server actions, API endpoints                                               |
| UI Library         | React 19.1.0     | Component rendering, hooks, and optimistic UI                                                   |
| Data Fetching      | React Query v5   | Smarter server-state management with caching, persistence, background sync, and offline support |
| Authentication     | Clerk            | User authentication, session management, JWT tokens with tenant claims                          |
| Backend & Database | Supabase 2.50.5  | PostgreSQL with RLS, realtime channels, edge functions for backend jobs                         |
| Client State       | Zustand 5.x      | Global reactive state management for UI-local state and tenant session info                     |
| Persistent Cache   | Dexie.js 4.x     | IndexedDB persistent cache with incremental delta sync and React Query persistence integration  |
| Validation         | Zod 3.x          | Schema validation for inputs and API responses                                                  |
| Styling            | Tailwind CSS 4.x | Utility-first CSS framework                                                                     |
| UI Components      | ShadCN UI        | Accessible, consistent, prebuilt UI components                                                  |

---

## 3. Updated High-Level Architecture Flow

```
┌─────────────────────────────────────────────────────────────────────────┐
│                            UI Layer (React 19)                         │
│  ┌───────────┐  ┌─────────────┐  ┌─────────────┐                       │
│  │ Tickets   │  │ Ticket View │  │ User Profile│                       │
│  └───────────┘  └─────────────┘  └─────────────┘                       │
└──────────────▲─────────────▲─────────────▲────────────────────────────┘
               │ Props/Events│ Props/Events│ Props/Events
┌──────────────▼─────────────▼─────────────▼────────────────────────────┐
│                 Client State Layer (Zustand + React Query)            │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ tenant_id: UUID                                                 │ │
│  │ UI state: modals, filters, tabs (Zustand)                       │ │
│  │ Server-state: queries, mutations (React Query v5)               │ │
│  │ queryOptions factory for consistent API calls                   │ │
│  │ Optimistic updates & background refetching                      │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└──────────────▲─────────────▲─────────────▲────────────────────────────┘
               │ Sync & Cache
┌──────────────▼─────────────▼─────────────▼────────────────────────────┐
│             Persistent Cache Layer (Dexie.js + React Query v5)         │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ IndexedDB for instant data load and offline support            │ │
│  │ React Query persistence (createPersister)                      │ │
│  │ Tenant-scoped tickets, responses, metadata                     │ │
│  │ Delta sync with backend and realtime events                    │ │
│  │ Smart cache querying with indexes for fast filtering/sorting  │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└──────────────▲─────────────▲─────────────▲────────────────────────────┘
               │ Realtime updates via Supabase Realtime, Server Actions
┌──────────────▼─────────────▼─────────────▼────────────────────────────┐
│                    Server Layer (Next.js Server Actions)              │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ Authentication with Clerk JWT                                   │ │
│  │ Tenant extraction and validation                                │ │
│  │ Zod input validation                                            │ │
│  │ Business logic: CRUD, notifications, audit logs                │ │
│  │ Serve filtered, paginated, sorted data for React Query         │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└──────────────▲─────────────▲─────────────▲────────────────────────────┘
               │ Realtime subscriptions and DB transactions (RLS)
┌──────────────▼─────────────▼─────────────▼────────────────────────────┐
│                         Database Layer (Supabase)                     │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │ PostgreSQL with tenant_id enforced on all scoped tables        │ │
│  │ Row Level Security (RLS) enforces tenant isolation             │ │
│  │ Realtime channels filtered by tenant and roles                 │ │
│  │ Edge functions for scheduled jobs (SLA, escalations)           │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────┘
```

---

## 4. Tenant Isolation & Security

- **Tenant ID (`tenant_id`)** is mandatory and enforced on all key tables (users, tickets, responses, audit logs).
- Supabase **Row Level Security (RLS)** policies enforce tenant-based data filtering using JWT claims issued by Clerk.
- React Query queries and mutations embed tenant context for all server requests.
- Backend API and server actions validate tenant context in JWT for every request, rejecting cross-tenant access.
- Soft deletes use status flags (e.g., `closed`) to preserve auditability and allow restoration.

---

## 5. React Query v5 Integration Strategy

- **Local First:** Queries pull from React Query cache or Dexie.js for instant UI rendering.
- **Realtime Updates:** Supabase realtime subscriptions trigger cache updates in React Query and Zustand.
- **Server Fallback:** For queries not fully satisfied locally, React Query fetches missing data pages.
- **QueryOptions Factory:** Centralize query/mutation options for DRY, type-safe, reusable patterns.
- **Persistence Layer:** Leverage React Query’s `createPersister` with Dexie.js for offline-first functionality.
- **Optimistic UI:** Mutations apply changes instantly in React Query and Zustand, sync backend asynchronously with rollback on failure.
- **Background Refetch:** React Query background sync keeps data fresh while user interacts smoothly.
- **Conflict Resolution:** Offline edits and sync conflicts are resolved with last-write-wins or custom tenant policies.
- **Cache Management:** Indexed queries by tenant_id and key fields for fast local queries.

---

## 6. Advantages of the Updated Architecture

- **Ultra-Responsive UI** with React Query caching and Dexie.js persistence.
- **Offline-First Support** using hybrid caching and sync.
- **Smarter Data Fetching**: Background refetches and realtime updates keep data fresh.
- **Type Safety & DRY Patterns** using queryOptions factory.
- **Strict Security** with RLS and tenant-aware query enforcement.
- **Optimized State Management**: Zustand for UI-local state, React Query for server state.
- **Enterprise-Grade Scalability** with modular stack and best practices for 2025.

---

## 7. Next Steps for Implementation

- Set up React Query v5 with Dexie.js persistence plugin.
- Build `queryOptions` factory for common API patterns.
- Integrate Supabase realtime events with React Query cache updates.
- Refactor server actions to provide tenant-aware data slices for React Query.
- Enhance Zustand stores for UI-local session and filtering state.
- Optimize React Query caching policies (e.g., `gcTime`, `staleTime`) for performance.

# Part 2: Authentication & Tenant-Aware User Management (Updated for React Query v5)

---

## 1. Authentication with Clerk (React Query Integrated)

- **Clerk** handles user sign-up, sign-in, session management, and identity verification.
- Clerk issues **JWT tokens** embedding essential claims: `user_id`, `tenant_id`, and `role` to establish user identity and tenant context.
- These tokens are included in all backend API requests and Next.js server actions, enabling strict tenant-aware authorization and access control.
- React Query’s `useQuery` hooks leverage these JWT tokens to fetch tenant-aware data securely.
- Clerk’s React hooks (e.g., `useUser()`) provide access to authenticated user info and claims on the frontend, which are passed as context to React Query queries and mutations.

---

## 2. User-Tenant Association & Syncing

- On user login or signup, backend synchronizes Clerk user data with the Supabase `users` table, associating users with their `tenant_id` and role.
- React Query queries the `users` table with tenant-aware filtering by passing the tenant ID from JWT claims.
- Syncing ensures that React Query’s cache stays consistent with backend changes.

| Field                      | Description                             |
| -------------------------- | --------------------------------------- |
| `id`                       | UUID primary key                        |
| `tenant_id`                | UUID foreign key to tenant              |
| `clerk_id`                 | Clerk user identifier                   |
| `email`                    | User email                              |
| `role`                     | Tenant-scoped role (user, agent, admin) |
| `created_at`, `updated_at` | Timestamps                              |

---

## 3. Tenant-Aware JWT Tokens & Claims

- JWT tokens include critical claims:

```json
{
  "sub": "user-uuid",
  "tenant_id": "tenant-uuid",
  "role": "agent",
  "iat": 1723547800,
  "exp": 1723551400
}
```

- React Query uses these claims as context for caching and refetching tenant-specific data.
- Backend APIs and Supabase RLS policies enforce tenant data isolation and role-based access control.
- Token validation ensures no cross-tenant or unauthorized access.

---

## 4. Role-Based Access Control (RBAC)

- Define tenant-scoped roles with clear permissions:

| Role            | Permissions                                          |
| --------------- | ---------------------------------------------------- |
| **User**        | Create tickets, view and respond to own tickets only |
| **Agent**       | Manage tickets assigned within their tenant/team     |
| **Admin**       | Manage users, teams, tickets within their tenant     |
| **Super Admin** | (Optional) Full system-wide access across tenants    |

- RBAC is enforced on both backend (API, RLS policies) and frontend (conditional rendering and UI controls).
- React Query uses role-specific query keys to ensure each user sees only authorized data.

---

## 5. Frontend Session and State Management

- Use Clerk’s React hooks (`useUser()`) to retrieve authenticated user data, including tenant ID and role.
- Store tenant and user metadata reactively in **Zustand** global state scoped to the user session.
- React Query hooks (`useQuery`, `useMutation`) consume tenant-aware context to ensure strict data isolation.
- Include JWT token in all React Query fetchers and backend API calls.

---

## 6. React Query v5 Integration in Authentication

- **React Query + Clerk Hooks**: Integrate Clerk session into React Query’s context providers for tenant-aware data fetching.
- Use **QueryOptions Factory** to generate type-safe query configurations that embed user and tenant claims.
- **Persistence Layer**: Store user session info securely in Dexie.js via React Query persistence.
- On logout or tenant switch, clear React Query and Dexie caches to prevent data leaks.
- Background sync refreshes JWT tokens and React Query cache automatically.

---

## 7. Security Considerations

- Always verify JWT token signatures and expiration on backend.
- Reject requests where the tenant ID in the JWT does not match the resource tenant.
- Do not expose tenant or user IDs in logs or error messages.
- Use HTTPS and secure token storage in frontend.
- Employ short-lived tokens with refresh mechanisms for improved security.
- React Query uses `onError` hooks to catch and handle auth-related errors gracefully.

---

# Part 3: Tenant-Aware Data Modeling & State Management (Updated for React Query v5)

---

## 1. Database Schema Design with Tenant Scope

- All tenant-specific tables include a mandatory **`tenant_id` UUID** field to enforce strict tenant data isolation.
- This tenant scoping is foundational for Row Level Security (RLS) policies in Supabase and ensures consistent application logic.
- React Query queries and mutations use tenant IDs from JWT tokens to enforce scoped data access.

### Key Tables and Tenant Columns

| Table        | Important Columns                                                                                                   | Description                    |
| ------------ | ------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| `users`      | `id`, `tenant_id`, `clerk_id`, `email`, `role`, `created_at`, `updated_at`                                          | Tenant-associated users        |
| `tickets`    | `id`, `tenant_id`, `user_id`, `subject`, `description`, `priority`, `category`, `status`, `deadline`, `assigned_to` | Tenant-scoped tickets          |
| `responses`  | `id`, `tenant_id`, `ticket_id`, `user_id`, `message`, `created_at`                                                  | Tenant-scoped ticket responses |
| `audit_logs` | `id`, `tenant_id`, `ticket_id`, `user_id`, `action_type`, `old_value`, `new_value`, `timestamp`                     | Tenant-scoped audit trail      |

---

## 2. Input Validation Using Zod

- Use **Zod** schemas to validate all incoming API data and requests.
- Enforce presence and correctness of `tenant_id` and other critical fields to avoid cross-tenant data leaks.
- React Query uses these schemas for type-safe validation of server responses and mutations.

Example ticket creation schema:

```ts
import { z } from 'zod';

export const createTicketSchema = z.object({
  tenant_id: z.string().uuid(),
  user_id: z.string().uuid(),
  subject: z.string().min(5),
  description: z.string().min(10),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  category: z.string().optional(),
});
```

---

## 3. Frontend State Management with Zustand and React Query

- **Zustand** manages tenant-aware UI state slices such as filters, modals, and active tabs.
- **React Query v5** manages server state (queries and mutations) with tenant-specific caching and invalidation strategies.
- Use **query keys** that include `tenant_id` to isolate caches across tenants.
- Tenant-aware hooks combine Zustand and React Query for smooth, reactive UIs.

Example Zustand store setup:

```ts
import create from 'zustand';

const useStore = create((set) => ({
  tenant_id: null,
  tickets: [],
  responses: {},
  users: [],
  setTenant: (tenantId) => set({ tenant_id: tenantId }),
  setTickets: (tickets) => set({ tickets }),
  addResponse: (ticketId, response) =>
    set((state) => ({
      responses: {
        ...state.responses,
        [ticketId]: [...(state.responses[ticketId] || []), response],
      },
    })),
  setUsers: (users) => set({ users }),
}));
```

---

## 4. Dexie.js Persistent Cache with React Query Integration

- Dexie.js is used for tenant-scoped caching in IndexedDB.
- React Query v5 integrates with Dexie.js via `createPersister` for offline support.
- Data loads instantly from Dexie on app startup and syncs in the background with server via React Query.
- Cache invalidation is triggered on logout or tenant switch to prevent cross-tenant data access.

---

## 5. Backend Data Access Patterns

- All backend API endpoints and server actions extract `tenant_id` from authenticated user JWT claims.
- React Query fetchers include `tenant_id` in every request for strict data scoping.
- Example SQL query filtering tickets by tenant:

```sql
SELECT * FROM tickets
WHERE tenant_id = current_setting('request.jwt.claim.tenant_id')::uuid;
```

---

## 6. Real-Time Updates with Supabase and React Query

- Supabase realtime channels push tenant-filtered data changes (insert/update/delete) to clients.
- React Query subscriptions update caches automatically.
- Zustand slices and Dexie caches are kept in sync via React Query’s `onSuccess` and `onSettled` hooks.

---

## 7. Soft Delete Strategy

- Soft deletes use status flags like `closed` instead of physical deletion.
- React Query refetches queries and purges closed tickets from active views.
- Closed tickets remain in IndexedDB and backend for auditing and potential restoration.

# Part 4: Ticket Lifecycle & Status Management with Tenant Isolation (Updated for React Query v5)

---

## 1. Ticket Status Definitions

- Tickets progress through a defined set of statuses, scoped within each tenant context:

| Status        | Description                                              |
| ------------- | -------------------------------------------------------- |
| **New**       | Created and awaiting assignment within the tenant        |
| **Open**      | Actively being worked on by tenant’s support agents      |
| **Pending**   | Waiting on tenant user input or additional information   |
| **Resolved**  | Issue believed fixed, awaiting tenant user confirmation  |
| **Closed**    | Finalized and archived (soft deleted), no further action |
| **Escalated** | Requires urgent attention due to SLA breach or priority  |

---

## 2. Status Transitions & Business Rules

- Transitions are strictly tenant-scoped and validated using tenant context from JWT tokens.

- React Query manages server-state mutations for status updates and ensures that cache updates propagate instantly to the UI.

- Typical lifecycle transitions:
  - `New → Open`: Agent picks up the ticket.
  - `Open → Pending`: Waiting on user input.
  - `Pending → Open`: User responds.
  - `Open → Resolved`: Agent marks as resolved.
  - `Resolved → Closed`: User confirms or timeout occurs.
  - Any status → `Escalated`: Triggered by SLA violations or urgent issues.

- Each status transition triggers:
  - React Query cache updates for tenant-specific queries.
  - Supabase realtime events to sync changes across all clients.
  - Tenant-scoped audit logs.

---

## 3. Soft Delete via Closed Status

- Implement soft delete by setting ticket status to `Closed`.
- Closed tickets are filtered from active views by React Query selectors but remain in backend and IndexedDB for audit and potential restoration.
- React Query automatically refetches queries when status changes to maintain cache consistency.

---

## 4. Tenant-Aware Audit Logging

- Every lifecycle event (status changes, escalations, soft deletes) is recorded with tenant context:

| Field         | Description                          |
| ------------- | ------------------------------------ |
| `tenant_id`   | Tenant owning the ticket             |
| `ticket_id`   | Ticket affected                      |
| `user_id`     | User performing the action           |
| `action_type` | e.g., `status_change`, `soft_delete` |
| `old_value`   | Previous status                      |
| `new_value`   | New status                           |
| `timestamp`   | Time of change                       |

---

## 5. SLA Enforcement & Escalations

- Backend jobs (e.g., Supabase Edge Functions) monitor SLA deadlines per tenant.
- Overdue tickets automatically escalate status and priority.
- React Query’s `refetchInterval` ensures UI reflects escalated statuses in near real-time.
- Notifications are sent to relevant tenant users, and all escalations are logged.

---

## 6. React Query v5 Integration in Status Management

- React Query mutations handle status updates and optimistic UI changes.
- Supabase realtime events synchronize status updates across all tenant users.
- Queries are invalidated and background refetched as needed to ensure data consistency.
- Use React Query’s `onMutate`, `onSuccess`, and `onSettled` hooks for smooth, tenant-aware lifecycle management.

# Part 5: Priority, Deadlines & Escalations with Tenant Isolation (Updated for React Query v5)

---

## 1. Priority Levels

- Each ticket has a priority level scoped to its tenant, indicating urgency and influencing SLA enforcement:

| Priority   | Description                                     |
| ---------- | ----------------------------------------------- |
| **Low**    | Minor issues; longer resolution time acceptable |
| **Medium** | Standard priority with typical SLA timelines    |
| **High**   | Requires quicker response and handling          |
| **Urgent** | Critical issues needing immediate attention     |

---

## 2. Setting Priorities

- Users select priority upon ticket creation.
- Tenant agents and admins can override priority to reflect actual urgency.
- A default priority (typically Medium) is assigned if none is specified.
- React Query uses priority-aware query keys and caching strategies to ensure UI updates accurately reflect priority levels.

---

## 3. Deadlines and Service Level Agreements (SLAs)

- Each priority level corresponds to a tenant-scoped SLA deadline:

| Priority | SLA Deadline |
| -------- | ------------ |
| Urgent   | 4 hours      |
| High     | 1 day        |
| Medium   | 3 days       |
| Low      | 7 days       |

- Deadlines are set on ticket creation or when priority changes.
- Backend jobs (e.g., Supabase Edge Functions) monitor these deadlines.

---

## 4. Automatic Priority Escalation

- Tenant-aware backend jobs scan tickets against SLA deadlines.
- Overdue tickets automatically escalate to a higher priority within their tenant scope.
- React Query’s `refetchInterval` ensures escalations are reflected in the UI without requiring manual refresh.
- Notifications are sent to the tenant’s support team members, and all escalations are logged.

---

## 5. Manual Priority Management

- Tenant agents and admins can manually update ticket priorities via UI or API.
- React Query mutations apply changes optimistically, update the local cache, and trigger background syncing with the backend.
- Supabase realtime subscriptions propagate these updates to all active clients.

---

## 6. React Query v5 Integration in Priority Management

- React Query hooks (`useMutation`, `useQuery`) handle priority changes and SLA deadline updates.
- Use `queryOptions` with tenant and priority context to optimize caching and refetching.
- Dexie.js persistence ensures priority changes are cached locally and sync correctly in offline scenarios.
- Supabase realtime updates ensure all client caches stay in sync across tenant users.

# Part 6: Roles, Teams, Auditing & Monitoring with Tenant Isolation (Updated for React Query v5)

---

## 1. User Roles and Permissions

- Define tenant-scoped roles to control user capabilities within their tenant context:

| Role              | Permissions                                                    |
| ----------------- | -------------------------------------------------------------- |
| **User**          | Create tickets, view/respond to own tickets only               |
| **Support Agent** | Manage tickets assigned to their tenant/team                   |
| **Admin**         | Manage users, teams, tickets, and settings within their tenant |
| **Super Admin**   | (Optional) Full system-wide access across tenants              |

- Roles are stored in the `users` table linked with `tenant_id`.
- Role enforcement is implemented in backend APIs, Supabase RLS policies, and frontend UI components.
- React Query queries and mutations are scoped using role-aware query keys to prevent unauthorized data access.

---

## 2. Team Management

- Support agents are grouped into tenant-scoped teams (e.g., Sales, Support).
- Teams include `tenant_id` for strict isolation.
- Tickets have an `assigned_to` field linked to users or teams within the tenant.
- Tenant admins can assign or reassign tickets via API calls, with React Query mutations ensuring the UI reflects changes immediately.

---

## 3. Audit Logging

- Tenant-aware audit logs record critical actions such as:
  - Ticket status changes
  - Priority updates
  - User management actions
  - Ticket assignments

| Field         | Description                         |
| ------------- | ----------------------------------- |
| `tenant_id`   | Tenant owning the action            |
| `ticket_id`   | Ticket affected (if applicable)     |
| `user_id`     | User performing the action          |
| `action_type` | e.g., `status_change`, `assignment` |
| `old_value`   | Previous value                      |
| `new_value`   | New value                           |
| `timestamp`   | Time of change                      |

- React Query uses `onSuccess` hooks to append new audit log entries to its cached queries after successful mutations.

---

## 4. Monitoring and Dashboards

- Tenant admins and super admins access dashboards showing:
  - Ticket volume and status distributions
  - Assignment and resolution metrics
  - SLA compliance and escalation trends
  - Audit log summaries filtered by tenant

- React Query queries fetch metrics and aggregate data for dashboards, leveraging caching for improved performance.

---

## 5. Optional Approval Workflows

- Tenant-scoped approval workflows for sensitive actions (e.g., priority escalations):
  - Agents submit change requests.
  - Tenant admins or super admins approve/reject them.
  - All approval activities are logged and React Query keeps the client UI in sync with backend state.

---

## 6. React Query v5 Integration for Roles and Monitoring

- Role-based API endpoints are consumed with React Query `queryOptions` to enforce access control.
- React Query devtools assist in monitoring tenant-scoped queries and cache state.
- Supabase realtime updates propagate role and team changes instantly to all connected clients.

# Part 7: Final Wrap-up and Best Practices (Updated for React Query v5)

---

## 1. Summary of Tenant-Aware Architecture

- The system is a **Domain-Driven Modular Monolith** supporting secure multi-tenancy.
- Tenant isolation is enforced across all layers: frontend, backend, database (Supabase RLS), and authentication (Clerk JWT claims).
- Hybrid client caching (Dexie.js), realtime updates (Supabase realtime), and **React Query v5** deliver a fast, scalable, and responsive experience.
- React Query v5 provides smarter server-state management with built-in persistence, background refetching, and type-safe API integration.
- Optimistic UI updates ensure instant feedback while mutations sync with the backend.
- Role-Based Access Control (RBAC) and team management control access and permissions effectively.
- Tenant-scoped audit logs and monitoring dashboards provide visibility and accountability.

---

## 2. Best Practices for React Query v5

### Tenant Isolation

- Include `tenant_id` in every query key to scope caching per tenant.
- Use Supabase RLS and JWT validation to prevent cross-tenant data leaks.

### React Query Patterns

- Centralize API calls with a **queryOptions factory** for consistent, type-safe configurations.
- Use `useQuery`, `useMutation`, and `useInfiniteQuery` hooks directly in components for declarative data fetching.
- Leverage `staleTime` and `gcTime` to optimize cache freshness.
- Integrate Dexie.js persistence using React Query’s `createPersister` for offline-first support.

### State Management

- Use React Query for server-state and Zustand for UI-local state.
- Sync Zustand and React Query caches for consistent UX.

### Performance and Security

- Enable React Query DevTools during development for cache monitoring.
- Minimize API overfetching by configuring `refetchOnWindowFocus` and background refetch intervals smartly.
- Secure all API calls with JWT tokens and validate on the server.

---

## 3. Operational Considerations

- Monitor tenant usage, SLA compliance, and performance via dashboards.
- Set up alerting for system health and tenant-specific metrics.
- Plan tenant onboarding workflows and role/permission audits.
- Regularly test caching and offline scenarios for robustness.

---

## 4. Future Enhancements

- Add tenant-specific feature flags and configurations.
- Extend approval workflows for critical actions.
- Introduce AI-driven automation and intelligent notifications.
- Decompose into microservices if scale demands while maintaining tenant context in queries.

---

## 5. Developer Collaboration

- Maintain domain modularity for parallel team development.
- Apply consistent React Query patterns and tenant scoping conventions.
- Automate testing for tenant isolation, RBAC, and data synchronization.
