import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';
import { createServiceSupabaseClient } from '@/lib/supabase-server';

// Validation schemas
const CreateDepartmentSchema = z.object({
  name: z
    .string()
    .min(1, 'Department name is required')
    .max(50, 'Department name too long')
    .trim(),
  color: z.string().min(1, 'Color is required'),
  dot_color: z.string().min(1, 'Dot color is required'),
  icon: z.string().optional().default('folder'),
  is_active: z.boolean().optional().default(true),
});

const UpdateDepartmentSchema = z.object({
  id: z.string().uuid('Invalid department ID'),
  name: z
    .string()
    .min(1, 'Department name is required')
    .max(50, 'Department name too long')
    .trim()
    .optional(),
  color: z.string().min(1, 'Color is required').optional(),
  dot_color: z.string().min(1, 'Dot color is required').optional(),
  icon: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Helper function to get authenticated user (for read operations)
async function getAuthenticatedUser(userId: string) {
  const serviceClient = createServiceSupabaseClient();

  const { data: currentUser, error: userError } = await serviceClient
    .from('users')
    .select('id, tenant_id, role')
    .eq('clerk_id', userId)
    .single();

  if (userError || !currentUser) {
    throw new Error('User not found');
  }

  return currentUser;
}

// Helper function to check admin permissions (for write operations)
async function checkAdminPermissions(userId: string) {
  const currentUser = await getAuthenticatedUser(userId);

  if (currentUser.role !== 'admin' && currentUser.role !== 'super_admin') {
    throw new Error('Admin access required');
  }

  return currentUser;
}

// GET - List departments for tenant (accessible to all authenticated users)
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Allow all authenticated users to read departments for dropdown functionality
    const currentUser = await getAuthenticatedUser(userId);
    const serviceClient = createServiceSupabaseClient();

    const { data: departments, error } = await serviceClient
      .from('tenant_departments')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .order('name');

    if (error) {
      console.error('Error fetching departments:', error);
      return NextResponse.json(
        { error: 'Failed to fetch departments' },
        { status: 500 }
      );
    }

    return NextResponse.json(departments);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    console.error('Error in GET /api/departments:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// POST - Create new department
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const currentUser = await checkAdminPermissions(userId);
    const requestBody = await request.json();

    const validationResult = CreateDepartmentSchema.safeParse(requestBody);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid department data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const departmentData = validationResult.data;
    const serviceClient = createServiceSupabaseClient();

    // Check if department name already exists for this tenant
    const { data: existingDept } = await serviceClient
      .from('tenant_departments')
      .select('id')
      .eq('tenant_id', currentUser.tenant_id)
      .eq('name', departmentData.name)
      .single();

    if (existingDept) {
      return NextResponse.json(
        { error: `Department "${departmentData.name}" already exists` },
        { status: 409 }
      );
    }

    const { data: newDepartment, error: insertError } = await serviceClient
      .from('tenant_departments')
      .insert({
        ...departmentData,
        tenant_id: currentUser.tenant_id,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating department:', insertError);
      return NextResponse.json(
        { error: 'Failed to create department' },
        { status: 500 }
      );
    }

    return NextResponse.json(newDepartment, { status: 201 });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    console.error('Error in POST /api/departments:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// PUT - Update department
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const currentUser = await checkAdminPermissions(userId);
    const requestBody = await request.json();

    const validationResult = UpdateDepartmentSchema.safeParse(requestBody);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid department data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { id, ...rawUpdateData } = validationResult.data;

    // Create updateData with only defined values
    const updateData: Record<string, string | boolean> = {};
    if (rawUpdateData.name !== undefined) updateData.name = rawUpdateData.name;
    if (rawUpdateData.color !== undefined)
      updateData.color = rawUpdateData.color;
    if (rawUpdateData.dot_color !== undefined)
      updateData.dot_color = rawUpdateData.dot_color;
    if (rawUpdateData.icon !== undefined) updateData.icon = rawUpdateData.icon;
    if (rawUpdateData.is_active !== undefined)
      updateData.is_active = rawUpdateData.is_active;
    const serviceClient = createServiceSupabaseClient();

    // Check if department exists and belongs to tenant
    const { data: existingDept, error: fetchError } = await serviceClient
      .from('tenant_departments')
      .select('id, name')
      .eq('id', id)
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (fetchError || !existingDept) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // If name is being updated, check for duplicates
    if (
      updateData.name &&
      typeof updateData.name === 'string' &&
      updateData.name !== existingDept.name
    ) {
      const { data: duplicateDept } = await serviceClient
        .from('tenant_departments')
        .select('id')
        .eq('tenant_id', currentUser.tenant_id)
        .eq('name', updateData.name)
        .neq('id', id)
        .single();

      if (duplicateDept) {
        return NextResponse.json(
          { error: `Department "${updateData.name}" already exists` },
          { status: 409 }
        );
      }
    }

    const { data: updatedDepartment, error: updateError } = await serviceClient
      .from('tenant_departments')
      .update(updateData)
      .eq('id', id)
      .eq('tenant_id', currentUser.tenant_id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating department:', updateError);
      return NextResponse.json(
        { error: 'Failed to update department' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedDepartment);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    console.error('Error in PUT /api/departments:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// DELETE - Delete department
export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const currentUser = await checkAdminPermissions(userId);
    const { searchParams } = new URL(request.url);
    const departmentId = searchParams.get('id');

    if (!departmentId) {
      return NextResponse.json(
        { error: 'Department ID is required' },
        { status: 400 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Check if department exists and belongs to tenant
    const { data: existingDept, error: fetchError } = await serviceClient
      .from('tenant_departments')
      .select('id, name')
      .eq('id', departmentId)
      .eq('tenant_id', currentUser.tenant_id)
      .single();

    if (fetchError || !existingDept) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Check if department has open tickets (not closed)
    // Use case-insensitive comparison since department names in tickets table are lowercase
    const { data: openTickets, error: ticketError } = await serviceClient
      .from('tickets')
      .select('id, title, status')
      .eq('tenant_id', currentUser.tenant_id)
      .ilike('department', existingDept.name)
      .neq('status', 'closed');

    if (ticketError) {
      console.error('Error checking open tickets:', ticketError);
      return NextResponse.json(
        { error: 'Failed to check department usage' },
        { status: 500 }
      );
    }

    if (openTickets && openTickets.length > 0) {
      const openTicketCount = openTickets.length;
      return NextResponse.json(
        {
          error: `Cannot delete "${existingDept.name}" department`,
          details: `You have ${openTicketCount} open ticket${openTicketCount > 1 ? 's' : ''} in ${existingDept.name} department. Please close them before deleting this department.`,
        },
        { status: 409 }
      );
    }

    // Check if department is used in auto-assignment rules
    const { data: rulesUsingDept, error: rulesError } = await serviceClient
      .from('auto_assignment_rules')
      .select('id')
      .eq('tenant_id', currentUser.tenant_id)
      .eq('department', existingDept.name)
      .limit(1);

    if (rulesError) {
      console.error('Error checking auto-assignment usage:', rulesError);
      return NextResponse.json(
        { error: 'Failed to check department usage' },
        { status: 500 }
      );
    }

    if (rulesUsingDept && rulesUsingDept.length > 0) {
      return NextResponse.json(
        {
          error: `Cannot delete "${existingDept.name}" department`,
          details:
            'This department has active auto-assignment rules. Please remove those rules first.',
        },
        { status: 409 }
      );
    }

    // Delete the department
    const { error: deleteError } = await serviceClient
      .from('tenant_departments')
      .delete()
      .eq('id', departmentId)
      .eq('tenant_id', currentUser.tenant_id);

    if (deleteError) {
      console.error('Error deleting department:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete department' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Department deleted successfully' });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    console.error('Error in DELETE /api/departments:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
