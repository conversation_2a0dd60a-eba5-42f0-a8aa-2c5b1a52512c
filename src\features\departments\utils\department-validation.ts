import { createServiceSupabaseClient } from '@/lib/supabase-server';

export interface DepartmentValidationResult {
  isValid: boolean;
  department?: {
    id: string;
    name: string;
    is_active: boolean;
  };
  error?: string;
}

/**
 * Validates that a department exists and is active for the given tenant
 * Used as middleware for ticket operations
 */
export async function validateDepartmentForTenant(
  tenantId: string,
  departmentName: string
): Promise<DepartmentValidationResult> {
  try {
    const serviceClient = createServiceSupabaseClient();

    // Check if department exists and is active for this tenant
    const { data: department, error } = await serviceClient
      .from('tenant_departments')
      .select('id, name, is_active')
      .eq('tenant_id', tenantId)
      .eq('name', departmentName)
      .single();

    if (error) {
      console.error('Error validating department:', error);
      return {
        isValid: false,
        error: `Department "${departmentName}" not found for this organization`,
      };
    }

    if (!department) {
      return {
        isValid: false,
        error: `Department "${departmentName}" does not exist in your organization`,
      };
    }

    if (!department.is_active) {
      return {
        isValid: false,
        error: `Department "${departmentName}" is currently inactive`,
      };
    }

    return {
      isValid: true,
      department,
    };
  } catch (error) {
    console.error('Department validation error:', error);
    return {
      isValid: false,
      error: 'Failed to validate department',
    };
  }
}

/**
 * Gets all active departments for a tenant (for fallback suggestions)
 */
export async function getActiveDepartmentsForTenant(
  tenantId: string
): Promise<string[]> {
  try {
    const serviceClient = createServiceSupabaseClient();

    const { data: departments, error } = await serviceClient
      .from('tenant_departments')
      .select('name')
      .eq('tenant_id', tenantId)
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching active departments:', error);
      return [];
    }

    return departments?.map((dept) => dept.name) || [];
  } catch (error) {
    console.error('Error fetching active departments:', error);
    return [];
  }
}

/**
 * Validates department and provides fallback suggestions if invalid
 */
export async function validateDepartmentWithFallback(
  tenantId: string,
  departmentName: string
): Promise<DepartmentValidationResult & { suggestions?: string[] }> {
  const validation = await validateDepartmentForTenant(
    tenantId,
    departmentName
  );

  if (!validation.isValid) {
    // Get active departments as suggestions
    const suggestions = await getActiveDepartmentsForTenant(tenantId);
    return {
      ...validation,
      suggestions,
    };
  }

  return validation;
}

/**
 * Middleware function for Express-like APIs to validate department
 * Can be used in ticket creation/update endpoints
 */
export function createDepartmentValidationMiddleware() {
  return async (
    tenantId: string,
    departmentName: string,
    options: {
      allowInactive?: boolean;
      provideSuggestions?: boolean;
    } = {}
  ) => {
    const { allowInactive = false, provideSuggestions = false } = options;

    try {
      const serviceClient = createServiceSupabaseClient();

      // Build query based on options
      let query = serviceClient
        .from('tenant_departments')
        .select('id, name, is_active')
        .eq('tenant_id', tenantId)
        .eq('name', departmentName);

      if (!allowInactive) {
        query = query.eq('is_active', true);
      }

      const { data: department, error } = await query.single();

      if (error || !department) {
        let errorMessage = `Department "${departmentName}" not found`;
        let suggestions: string[] = [];

        if (provideSuggestions) {
          suggestions = await getActiveDepartmentsForTenant(tenantId);
          if (suggestions.length > 0) {
            errorMessage += `. Available departments: ${suggestions.join(', ')}`;
          }
        }

        return {
          isValid: false,
          error: errorMessage,
          suggestions: provideSuggestions ? suggestions : undefined,
        };
      }

      return {
        isValid: true,
        department,
      };
    } catch (error) {
      console.error('Department validation middleware error:', error);
      return {
        isValid: false,
        error: 'Failed to validate department',
      };
    }
  };
}
