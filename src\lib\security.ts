export function generateCsp(nonce: string, hostname?: string): string {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseHost = new URL(supabaseUrl).hostname;

  // Check if we're running on localhost (development or production testing)
  const isLocalhost =
    hostname?.includes('localhost') || hostname?.includes('127.0.0.1');

  // For localhost, we need to allow 'unsafe-inline' for Next.js production builds
  // This is because Next.js generates inline scripts that don't use nonces in production mode
  // Note: When nonce is present, 'unsafe-inline' is ignored, so we omit nonce for localhost
  const scriptSrc = isLocalhost
    ? `'self' 'unsafe-inline' 'unsafe-eval' https://clerk.catalystrcm.com https://*.clerk.accounts.dev https://${supabaseHost}`
    : `'self' 'nonce-${nonce}' https://clerk.catalystrcm.com https://*.clerk.accounts.dev https://${supabaseHost} 'unsafe-inline' ${
        process.env.NODE_ENV === 'development' ? "'unsafe-eval'" : ''
      }`;

  const csp = [
    "default-src 'self'",
    `script-src ${scriptSrc}`,
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: blob: https://img.clerk.com https://images.clerk.dev",
    "font-src 'self' data: https://fonts.gstatic.com",
    `connect-src 'self' https://clerk.catalystrcm.com https://*.clerk.accounts.dev https://${supabaseHost} wss://${supabaseHost} https://*.supabase.co wss://*.supabase.co https://clerk-telemetry.com${
      process.env.NODE_ENV === 'development' || isLocalhost
        ? ' http://localhost:* ws://localhost:*'
        : ''
    }`,
    "frame-src 'self' https://clerk.catalystrcm.com https://*.clerk.accounts.dev",
    "worker-src 'self' blob:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    // Only apply strict security headers for non-localhost environments
    ...(isLocalhost
      ? []
      : ['block-all-mixed-content', 'upgrade-insecure-requests']),
  ].join('; ');

  return csp;
}
