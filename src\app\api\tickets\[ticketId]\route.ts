import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { z } from 'zod';
import { validateDepartmentWithFallback } from '@/features/departments/utils/department-validation';

// Validation schema for query parameters
const TicketDetailQuerySchema = z.object({
  tenant_id: z.string().min(1, 'tenant_id is required'),
});

// Validation schema for ticket updates
const TicketUpdateSchema = z.object({
  tenant_id: z.string().min(1, 'tenant_id is required'),
  status: z.enum(['new', 'open', 'pending', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  department: z
    .string()
    .min(1, 'Department is required')
    .max(50, 'Department name too long')
    .optional(),
  title: z.string().min(1).max(200).trim().optional(),
  description: z.string().min(1).max(5000).trim().optional(),
});

// Helper function to get tenant UUID from subdomain
async function getTenantUuid(
  serviceSupabase: ReturnType<typeof createServiceSupabaseClient>,
  tenantParam: string
) {
  if (tenantParam.includes('-')) {
    return tenantParam; // Already a UUID
  }

  const { data: tenantData, error: tenantError } = await serviceSupabase
    .from('tenants')
    .select('id')
    .eq('subdomain', tenantParam)
    .single();

  if (tenantError || !tenantData) {
    throw new Error(`Tenant '${tenantParam}' not found`);
  }
  return tenantData.id;
}

// Helper function to validate user access
async function validateUserAccess(
  serviceSupabase: ReturnType<typeof createServiceSupabaseClient>,
  userId: string,
  tenantUuid: string
) {
  const { data: userData, error: userError } = await serviceSupabase
    .from('users')
    .select('id, tenant_id, role, status, first_name, last_name, email')
    .eq('clerk_id', userId)
    .single();

  if (userError || !userData) {
    throw new Error('User not found');
  }
  if (userData.tenant_id !== tenantUuid) {
    throw new Error('Access denied to this tenant');
  }
  return userData;
}

// GET method for fetching individual ticket details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    // Await params before using
    const { ticketId } = await params;

    // Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get tenant_id from query parameters
    const { searchParams } = new URL(request.url);
    const tenantParam = searchParams.get('tenant_id');

    if (!tenantParam) {
      return NextResponse.json(
        { error: 'tenant_id parameter is required' },
        { status: 400 }
      );
    }

    // Validate query parameters
    const validationResult = TicketDetailQuerySchema.safeParse({
      tenant_id: tenantParam,
    });

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: validationResult.error.issues },
        { status: 400 }
      );
    }

    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(serviceSupabase, tenantParam);
    await validateUserAccess(serviceSupabase, userId, tenantUuid);

    // Fetch the specific ticket with all related data
    const { data: ticket, error: ticketError } = await serviceSupabase
      .from('tickets')
      .select(
        `
        *,
        users!tickets_created_by_fkey (
          id,
          clerk_id,
          first_name,
          last_name,
          email,
          role,
          avatar_url
        ),
        assigned_user:users!tickets_assigned_to_fkey (
          id,
          clerk_id,
          first_name,
          last_name,
          email,
          role,
          avatar_url
        )
      `
      )
      .eq('id', ticketId)
      .eq('tenant_id', tenantUuid)
      .single();

    if (ticketError || !ticket) {
      return NextResponse.json(
        { error: 'Ticket not found or access denied' },
        { status: 404 }
      );
    }

    // Transform the ticket data to match frontend expectations
    const transformedTicket = {
      id: ticket.id,
      tenantId: tenantParam,
      title: ticket.title,
      description: ticket.description,
      status: ticket.status,
      priority: ticket.priority,
      department: ticket.department,
      createdAt: new Date(ticket.created_at || new Date()),
      updatedAt: new Date(ticket.updated_at || new Date()),
      userId: ticket.created_by,
      creatorClerkId: ticket.users?.clerk_id,
      userName:
        `${ticket.users?.first_name || ''} ${ticket.users?.last_name || ''}`.trim() ||
        ticket.users?.email?.split('@')[0] ||
        `User-${ticket.users?.id?.substring(0, 8) || 'unknown'}`,
      userEmail: ticket.users?.email || '<EMAIL>',
      userAvatar: ticket.users?.avatar_url || undefined,
      assignedTo: ticket.assigned_to,
      assignedUser: ticket.assigned_user
        ? {
            id: ticket.assigned_user.id,
            clerkId: ticket.assigned_user.clerk_id,
            name: `${ticket.assigned_user.first_name || ''} ${ticket.assigned_user.last_name || ''}`.trim(),
            email: ticket.assigned_user.email,
            avatar: ticket.assigned_user.avatar_url,
            role: ticket.assigned_user.role,
          }
        : null,
      metadata: ticket.metadata || {},
      messages: [], // Messages are fetched separately via the messages endpoint
      attachments: [], // Attachments would be fetched separately if needed
    };

    return NextResponse.json({
      success: true,
      data: transformedTicket,
    });
  } catch (error) {
    console.error('Error fetching ticket detail:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// PATCH method for updating ticket properties
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    // Await params before using
    const { ticketId } = await params;

    // Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate input
    const validationResult = TicketUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid update data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;
    const tenantParam = updateData.tenant_id;

    // Create service client and validate tenant/user access
    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(serviceSupabase, tenantParam);
    await validateUserAccess(serviceSupabase, userId, tenantUuid);

    // Validate department if it's being updated
    if (updateData.department) {
      const departmentValidation = await validateDepartmentWithFallback(
        tenantUuid,
        updateData.department
      );

      if (!departmentValidation.isValid) {
        return NextResponse.json(
          {
            error: departmentValidation.error,
            suggestions: departmentValidation.suggestions,
          },
          { status: 400 }
        );
      }
    }

    // Prepare update object (remove tenant_id as it's not a column to update)
    const { tenant_id: _, ...rawFieldsToUpdate } = updateData;

    // Filter out undefined values and add updated_at timestamp
    const updateObject: Record<string, string | boolean | null> = {
      updated_at: new Date().toISOString(),
    };

    // Only include defined values
    Object.entries(rawFieldsToUpdate).forEach(([key, value]) => {
      if (value !== undefined) {
        updateObject[key] = value;
      }
    });

    // Update the ticket
    const { data: updatedTicket, error: updateError } = await serviceSupabase
      .from('tickets')
      .update(updateObject)
      .eq('id', ticketId)
      .eq('tenant_id', tenantUuid)
      .select('*')
      .single();

    if (updateError) {
      console.error('Failed to update ticket:', updateError);
      return NextResponse.json(
        { error: 'Failed to update ticket' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Ticket updated successfully',
      data: updatedTicket,
    });
  } catch (error) {
    console.error('Error updating ticket:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
