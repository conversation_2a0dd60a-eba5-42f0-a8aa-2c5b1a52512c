# Real-time Connection Test Plan

## Test: Single Connection Enforcement

### Expected Behavior

When the tickets page loads, it should:

1. **Create only ONE subscription** per tenant, even though multiple hooks use real-time data:
   - `useRealtimeTickets` (ticket list)
   - `useRealtimeTicket` (selected ticket detail)
   - `useTicketMessages` (ticket messages)

2. **Show subscription reuse** in console logs:
   - First hook: "🚀 First subscriber for tenant {uuid} - creating subscription"
   - Second hook: "♻️ Reusing existing subscription for tenant {uuid}"
   - Third hook: "♻️ Reusing existing subscription for tenant {uuid}"

3. **Use unified channel name**: `unified-realtime-{tenantUuid}`

4. **Proper cleanup** when components unmount:
   - "🔌 Removed subscriber" messages
   - "🛑 No more subscribers" when last component unmounts

### Test Steps

1. **Open the tickets page** in browser
2. **Check browser console** for subscription logs
3. **Verify debug panel** shows correct information
4. **Navigate away and back** to test cleanup/recreation
5. **Open multiple tickets** to test cache consistency

### Expected Console Output

```
🔗 Added subscriber subscriber-{timestamp}-{random} to tenant {uuid}. Total subscribers: 1
🚀 First subscriber for tenant {uuid} - creating subscription
🔄 Creating unified real-time subscription for tenant: {uuid}
📡 Channel name: unified-realtime-{uuid}
👥 Subscriber count: 1

🔗 Added subscriber subscriber-{timestamp}-{random} to tenant {uuid}. Total subscribers: 2
♻️ Reusing existing subscription for tenant {uuid}

🔗 Added subscriber subscriber-{timestamp}-{random} to tenant {uuid}. Total subscribers: 3
♻️ Reusing existing subscription for tenant {uuid}
```

### Success Criteria

✅ **Single Connection**: Only one Supabase channel created per tenant
✅ **Subscription Reuse**: Additional hooks reuse existing subscription
✅ **Proper Logging**: Clear logs showing subscriber management
✅ **Data Consistency**: All components receive real-time updates
✅ **Clean Cleanup**: Proper subscription destruction when no subscribers

### Failure Indicators

❌ Multiple channels created for same tenant
❌ No "reusing existing subscription" messages
❌ Subscription not destroyed when components unmount
❌ Data inconsistency between ticket list and detail views

## Test: Data Consistency

### Test Steps

1. **Open ticket list** (shows multiple tickets)
2. **Select a ticket** (opens detail view)
3. **In another browser/tab**, update the selected ticket
4. **Verify both views update** simultaneously

### Expected Behavior

- Ticket list shows updated status/priority
- Ticket detail shows updated information
- No duplicate API calls or subscriptions
- Updates appear instantly in both views

## Manual Testing Commands

To test the implementation:

```bash
# 1. Start the development server
npm run dev

# 2. Open browser to tickets page
# http://localhost:3000/tickets

# 3. Open browser console and look for real-time logs

# 4. Check the debug panel in bottom-right corner
```

## Automated Testing (Future)

```typescript
// Example test for subscription enforcement
describe('Unified Real-time Subscription', () => {
  it('should create only one subscription per tenant', () => {
    // Mock Supabase client
    // Render components that use real-time hooks
    // Verify only one channel is created
    // Verify subscriber count increases
  });

  it('should reuse existing subscriptions', () => {
    // Test that additional hooks reuse existing subscription
  });

  it('should clean up properly on unmount', () => {
    // Test subscription destruction when no subscribers
  });
});
```
