import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { getDomainFromWindow, DomainInfoState } from '@/lib/domain';

export function useHomeRedirect() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [domainInfo, setDomainInfo] = useState<DomainInfoState>(null);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    setDomainInfo(getDomainFromWindow(window));
  }, []);

  useEffect(() => {
    if (!isLoaded || !domainInfo) return;

    if (domainInfo.isLocalhost && !domainInfo.isSubdomain) {
      setShouldRender(true);
      return;
    }

    if (domainInfo.isSubdomain) {
      if (user) {
        router.replace('/tickets');
      } else {
        router.replace('/sign-in');
      }
      return;
    }

    router.replace('/sign-in');
  }, [isLoaded, user, domainInfo, router]);

  return {
    isLoaded: isLoaded && !!domainInfo,
    shouldRender,
  };
}
